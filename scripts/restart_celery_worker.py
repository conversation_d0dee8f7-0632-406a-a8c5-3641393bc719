#!/usr/bin/env python3
"""
Celery Worker重启脚本
"""
import os
import sys
import time
import signal
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.celery_app import celery_app
from app.log import logger


def find_celery_processes():
    """查找Celery进程"""
    try:
        result = subprocess.run(
            ['ps', 'aux'], 
            capture_output=True, 
            text=True, 
            check=True
        )
        
        celery_processes = []
        for line in result.stdout.split('\n'):
            if 'celery' in line and 'worker' in line and 'app.core.celery_app' in line:
                parts = line.split()
                if len(parts) > 1:
                    pid = int(parts[1])
                    celery_processes.append(pid)
        
        return celery_processes
    except Exception as e:
        logger.error(f"Failed to find Celery processes: {e}")
        return []


def stop_celery_workers():
    """停止Celery Worker"""
    processes = find_celery_processes()
    
    if not processes:
        print("No Celery worker processes found")
        return True
    
    print(f"Found {len(processes)} Celery processes: {processes}")
    
    # 尝试优雅停止
    for pid in processes:
        try:
            print(f"Sending TERM signal to process {pid}")
            os.kill(pid, signal.SIGTERM)
        except ProcessLookupError:
            print(f"Process {pid} already terminated")
        except Exception as e:
            print(f"Failed to terminate process {pid}: {e}")
    
    # 等待进程停止
    print("Waiting for processes to stop...")
    time.sleep(5)
    
    # 检查是否还有进程运行
    remaining = find_celery_processes()
    if remaining:
        print(f"Force killing remaining processes: {remaining}")
        for pid in remaining:
            try:
                os.kill(pid, signal.SIGKILL)
            except ProcessLookupError:
                pass
            except Exception as e:
                print(f"Failed to kill process {pid}: {e}")
        
        time.sleep(2)
    
    # 最终检查
    final_check = find_celery_processes()
    if final_check:
        print(f"Warning: Some processes may still be running: {final_check}")
        return False
    else:
        print("All Celery processes stopped")
        return True


def start_celery_worker():
    """启动Celery Worker"""
    try:
        print("Starting Celery worker...")
        
        # 构建启动命令
        cmd = [
            'celery', '-A', 'app.core.celery_app', 'worker',
            '--loglevel=info',
            '--concurrency=4',
            '--queues=download,processing,management',
            '--max-tasks-per-child=1000',
            '--prefetch-multiplier=1'
        ]
        
        print(f"Command: {' '.join(cmd)}")
        
        # 启动进程
        process = subprocess.Popen(
            cmd,
            cwd=str(project_root),
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        print(f"Celery worker started with PID: {process.pid}")
        
        # 等待一段时间检查启动状态
        time.sleep(3)
        
        if process.poll() is None:
            print("✅ Celery worker started successfully")
            return True
        else:
            print("❌ Celery worker failed to start")
            output, _ = process.communicate()
            print(f"Output: {output}")
            return False
            
    except Exception as e:
        print(f"❌ Failed to start Celery worker: {e}")
        return False


def test_worker():
    """测试Worker是否正常工作"""
    try:
        print("Testing worker...")
        
        from app.core.celery.download_tasks import test_download_task
        
        # 提交测试任务
        result = test_download_task.delay("test_url")
        print(f"Test task submitted: {result.id}")
        
        # 等待一段时间检查状态
        for i in range(10):
            status = result.status
            print(f"Task status: {status}")
            
            if status != 'PENDING':
                print(f"✅ Worker is processing tasks (status: {status})")
                return True
            
            time.sleep(1)
        
        print("❌ Task still PENDING after 10 seconds")
        return False
        
    except Exception as e:
        print(f"❌ Failed to test worker: {e}")
        return False


def main():
    """主函数"""
    print("🔄 Restarting Celery Worker...")
    print("=" * 50)
    
    # 1. 停止现有Worker
    print("Step 1: Stopping existing workers...")
    if not stop_celery_workers():
        print("⚠️  Warning: Some workers may not have stopped properly")
    
    print()
    
    # 2. 启动新Worker
    print("Step 2: Starting new worker...")
    if not start_celery_worker():
        print("❌ Failed to start worker")
        return 1
    
    print()
    
    # 3. 测试Worker
    print("Step 3: Testing worker...")
    if test_worker():
        print("✅ Celery worker restart completed successfully!")
        return 0
    else:
        print("❌ Worker test failed")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
