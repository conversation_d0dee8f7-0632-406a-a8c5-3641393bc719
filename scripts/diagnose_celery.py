#!/usr/bin/env python3
"""
Celery问题诊断脚本
"""
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.celery_app import celery_app
from app.core.celery.download_tasks import test_download_task


def check_celery_config():
    """检查Celery配置"""
    print("🔍 Celery配置检查")
    print("-" * 30)
    
    print(f"Broker URL: {celery_app.conf.broker_url}")
    print(f"Result Backend: {celery_app.conf.result_backend}")
    print(f"Task Serializer: {celery_app.conf.task_serializer}")
    print(f"Accept Content: {celery_app.conf.accept_content}")
    
    print("\n任务路由:")
    for task_name, route in celery_app.conf.task_routes.items():
        print(f"  {task_name} -> {route}")
    
    print("\n已注册任务:")
    for task_name in sorted(celery_app.tasks.keys()):
        if not task_name.startswith('celery.'):
            print(f"  - {task_name}")


def check_worker_status():
    """检查Worker状态"""
    print("\n🔍 Worker状态检查")
    print("-" * 30)
    
    try:
        inspect = celery_app.control.inspect()
        
        # 检查活跃worker
        active_workers = inspect.active()
        if not active_workers:
            print("❌ 没有发现活跃的Worker")
            return False
        
        print(f"✅ 发现 {len(active_workers)} 个活跃Worker:")
        for worker_name, tasks in active_workers.items():
            print(f"  - {worker_name}: {len(tasks)} 个活跃任务")
        
        # 检查队列
        active_queues = inspect.active_queues()
        if active_queues:
            print("\n队列状态:")
            for worker_name, queues in active_queues.items():
                print(f"  {worker_name}:")
                for queue in queues:
                    print(f"    - {queue['name']}")
        
        # 检查注册的任务
        registered = inspect.registered()
        if registered:
            print("\nWorker注册的任务:")
            for worker_name, tasks in registered.items():
                print(f"  {worker_name}: {len(tasks)} 个任务")
                for task in sorted(tasks):
                    if not task.startswith('celery.'):
                        print(f"    - {task}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查Worker状态失败: {e}")
        return False


def test_task_submission():
    """测试任务提交"""
    print("\n🔍 任务提交测试")
    print("-" * 30)
    
    try:
        # 提交测试任务
        print("提交测试任务...")
        result = test_download_task.delay("http://example.com/test.jpg")
        print(f"任务ID: {result.id}")
        print(f"初始状态: {result.status}")
        
        # 监控任务状态变化
        print("\n监控任务状态变化:")
        for i in range(15):  # 监控15秒
            status = result.status
            print(f"  {i+1:2d}s: {status}")
            
            if status == 'SUCCESS':
                print("✅ 任务执行成功!")
                try:
                    print(f"结果: {result.result}")
                except Exception as e:
                    print(f"获取结果失败: {e}")
                return True
            elif status == 'FAILURE':
                print("❌ 任务执行失败!")
                try:
                    print(f"错误: {result.traceback}")
                except Exception as e:
                    print(f"获取错误信息失败: {e}")
                return False
            elif status != 'PENDING':
                print(f"任务状态变为: {status}")
            
            time.sleep(1)
        
        print("❌ 任务在15秒内未完成")
        return False
        
    except Exception as e:
        print(f"❌ 任务提交失败: {e}")
        return False


def check_redis_connection():
    """检查Redis连接"""
    print("\n🔍 Redis连接检查")
    print("-" * 30)
    
    try:
        # 尝试连接Redis
        conn = celery_app.connection()
        conn.ensure_connection(max_retries=3)
        print("✅ Redis连接正常")
        conn.close()
        return True
    except Exception as e:
        print(f"❌ Redis连接失败: {e}")
        return False


def main():
    """主函数"""
    print("🔧 Celery问题诊断工具")
    print("=" * 50)
    
    # 检查配置
    check_celery_config()
    
    # 检查Redis连接
    redis_ok = check_redis_connection()
    
    # 检查Worker状态
    worker_ok = check_worker_status()
    
    # 测试任务提交
    task_ok = test_task_submission()
    
    # 总结
    print("\n📋 诊断总结")
    print("-" * 30)
    print(f"Redis连接: {'✅ 正常' if redis_ok else '❌ 异常'}")
    print(f"Worker状态: {'✅ 正常' if worker_ok else '❌ 异常'}")
    print(f"任务执行: {'✅ 正常' if task_ok else '❌ 异常'}")
    
    if redis_ok and worker_ok and task_ok:
        print("\n🎉 Celery系统运行正常!")
        return 0
    else:
        print("\n⚠️  发现问题，请检查上述输出")
        
        # 提供建议
        print("\n💡 建议:")
        if not redis_ok:
            print("  - 检查Redis服务是否运行")
            print("  - 检查REDIS_URL配置是否正确")
        if not worker_ok:
            print("  - 启动Celery Worker: celery -A app.core.celery_app worker")
            print("  - 检查Worker日志是否有错误")
        if not task_ok:
            print("  - 重启Celery Worker")
            print("  - 检查任务路由配置")
            print("  - 查看Worker日志")
        
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
