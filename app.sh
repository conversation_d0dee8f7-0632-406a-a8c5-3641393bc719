#!/bin/bash

# 配置
APP_NAME="AI Platform"
CONDA_ENV="ai_platform"
PID_FILE="./run/app.pid"
GUNICORN_CONF="gunicorn_conf.py"

# 颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 创建必要的目录
mkdir -p ./run

# 获取时间戳
get_timestamp() {
    date +"%Y%m%d_%H%M%S"
}

# 输出带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查服务状态
check_status() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p $pid > /dev/null; then
            print_message "$GREEN" "[$APP_NAME] 正在运行 (PID: $pid)"
            return 0
        else
            print_message "$YELLOW" "[$APP_NAME] 进程已死亡，但 PID 文件仍存在"
            rm -f "$PID_FILE"
            return 1
        fi
    else
        print_message "$RED" "[$APP_NAME] 未运行"
        return 1
    fi
}

# 检查API是否可访问
check_api() {
    local port=${1:-$APP_PORT}
    if curl -s "http://localhost:$port/api/v1/health" > /dev/null; then
        return 0
    else
        print_message "$RED" "[$APP_NAME] API 服务不可访问"
        return 1
    fi
}

# 启动服务
start() {
    # 获取环境参数，默认为prod
    local env=${2:-"prod"}
    
    if check_status > /dev/null; then
        print_message "$YELLOW" "[$APP_NAME] 已经在运行中"
        return 1
    fi

    print_message "$GREEN" "正在启动 $APP_NAME (环境: $env)..."

    # 激活 conda 环境
    source /home/<USER>/anaconda3/bin/activate $CONDA_ENV

    # 设置日志文件
    local log_file="./run/app.log"

    # 设置环境变量（关闭调试模式）
    export DEBUG=False
    
    # 使用统一配置文件 (8个workers)
    local conf_file=$GUNICORN_CONF
    print_message "$GREEN" "使用统一配置 (8个workers + Celery后端)"
    
    # 使用gunicorn启动应用
    print_message "$GREEN" "使用配置文件 $conf_file 启动 Gunicorn..."
    nohup gunicorn -c $conf_file main:app > "${log_file}" 2>&1 &

    # 保存 PID
    echo $! > "$PID_FILE"

    print_message "$GREEN" "[$APP_NAME] 已启动 (使用 Gunicorn + Uvicorn workers)"
    if [ "$env" == "test" ]; then
        print_message "$GREEN" "环境: 测试环境"
        print_message "$GREEN" "进程数: 3"
    else
        print_message "$GREEN" "环境: 生产环境"
    fi
    print_message "$GREEN" "PID: $(cat $PID_FILE)"
    print_message "$GREEN" "日志文件: ${log_file}"
}

# 停止服务
stop() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p $pid > /dev/null; then
            print_message "$YELLOW" "正在停止 $APP_NAME (PID: $pid)..."
            kill $pid

            # 等待进程结束
            local count=0
            while ps -p $pid > /dev/null && [ $count -lt 10 ]; do
                sleep 1
                ((count++))
            done

            if ps -p $pid > /dev/null; then
                print_message "$RED" "无法正常停止进程，尝试强制终止..."
                kill -9 $pid
            fi

            rm -f "$PID_FILE"
            print_message "$GREEN" "[$APP_NAME] 已停止"
        else
            print_message "$YELLOW" "[$APP_NAME] 进程已不存在"
            rm -f "$PID_FILE"
        fi
    else
        print_message "$RED" "[$APP_NAME] 未运行"
    fi
}

# 重启服务
restart() {
    print_message "$YELLOW" "正在重启 $APP_NAME..."
    stop
    sleep 2
    start $@  # 传递所有参数，包括可能的环境参数
}

# 同步插件
sync() {
    # 从.env文件中读取环境变量
    if [ -f ".env" ]; then
        APP_PORT=$(grep "^APP_PORT=" .env | cut -d= -f2)
        STORAGE_PATH=$(grep "^STORAGE_PATH=" .env | cut -d= -f2)
        PLUGIN_STORAGE_PATH=$(grep "^PLUGIN_STORAGE_PATH=" .env | cut -d= -f2)
    else
        print_message "$RED" "[$APP_NAME] .env文件不存在"
        return 1
    fi

    # 检查必要的环境变量
    if [ -z "$APP_PORT" ] || [ -z "$STORAGE_PATH" ] || [ -z "$PLUGIN_STORAGE_PATH" ]; then
        print_message "$RED" "[$APP_NAME] 无法从.env文件读取必要的环境变量"
        return 1
    fi
    
    # 构建完整的插件目录路径
    FULL_PLUGIN_PATH="${STORAGE_PATH}/${PLUGIN_STORAGE_PATH}"
    
    # 检查插件目录是否存在
    if [ ! -d "$FULL_PLUGIN_PATH" ]; then
        print_message "$RED" "[$APP_NAME] 插件目录不存在: $FULL_PLUGIN_PATH"
        return 1
    fi

    # 输出操作信息
    print_message "$GREEN" "开始同步插件..."
    print_message "$GREEN" "使用端口: $APP_PORT"
    print_message "$GREEN" "存储路径: $STORAGE_PATH"
    print_message "$GREEN" "插件路径: $FULL_PLUGIN_PATH"
    
    # 检查API服务是否可访问
    if ! check_api "$APP_PORT"; then
        print_message "$RED" "[$APP_NAME] 请确保应用已启动且API服务可访问"
        return 1
    fi

    # 根据参数决定同步方式
    if [ $# -eq 0 ]; then
        # 同步所有插件
        print_message "$GREEN" "同步所有插件的最新版本..."
        
        # 遍历插件目录
        for plugin_dir in $(find "$FULL_PLUGIN_PATH" -maxdepth 1 -type d | grep -v "^$FULL_PLUGIN_PATH$"); do
            plugin_name=$(basename "$plugin_dir")
            
            # 跳过特殊目录
            if [[ "$plugin_name" == "." || "$plugin_name" == ".." || "$plugin_name" == ".git" ]]; then
                continue
            fi
            
            print_message "$YELLOW" "处理插件: $plugin_name"
            
            # 查找最新版本目录
            latest_version=""
            for version_dir in $(find "$plugin_dir" -maxdepth 1 -type d | grep -v "^$plugin_dir$" | sort -V); do
                version=$(basename "$version_dir")
                
                # 跳过非版本目录
                if [[ "$version" == "." || "$version" == ".." || "$version" == "data" ]]; then
                    continue
                fi
                
                latest_version="$version"
            done
            
            if [ -n "$latest_version" ]; then
                # 使用相对路径构建插件路径（相对于STORAGE_PATH）
                plugin_path="$PLUGIN_STORAGE_PATH/$plugin_name/$latest_version"
                print_message "$GREEN" "同步插件版本: $plugin_name:$latest_version"
                
                # 调用API同步插件
                response=$(curl -s -X POST "http://localhost:$APP_PORT/api/v1/plugins/sync" \
                    -H "Content-Type: application/json" \
                    -d "{\"pluginDirPath\": \"$plugin_path\", \"updateIfNewer\": true}")
                
                # 检查响应
                if echo "$response" | grep -q "\"success\":true"; then
                    print_message "$GREEN" "插件 $plugin_name:$latest_version 同步成功"
                else
                    print_message "$RED" "插件 $plugin_name:$latest_version 同步失败: $response"
                fi
            else
                print_message "$RED" "插件 $plugin_name 没有找到有效的版本"
            fi
        done
    else
        # 同步指定的插件
        for plugin_path in "$@"; do
            # 检查路径是否为绝对路径
            if [[ "$plugin_path" = /* ]]; then
                # 绝对路径，检查文件是否存在
                if [ -d "$plugin_path" ]; then
                    full_path="$plugin_path"
                    # 计算相对于STORAGE_PATH的路径
                    if [[ "$plugin_path" == "$STORAGE_PATH"* ]]; then
                        # 从绝对路径中提取相对路径部分
                        rel_path="${plugin_path#$STORAGE_PATH/}"
                    else
                        # 如果不是STORAGE_PATH的子路径，使用完整路径
                        rel_path="$plugin_path"
                    fi
                else
                    print_message "$RED" "指定的插件路径不存在: $plugin_path"
                    continue
                fi
            else
                # 相对路径，检查是否存在
                if [ -d "$STORAGE_PATH/$plugin_path" ]; then
                    full_path="$STORAGE_PATH/$plugin_path"
                    rel_path="$plugin_path"
                elif [ -d "$plugin_path" ]; then
                    # 相对于当前目录的路径
                    full_path="$(pwd)/$plugin_path"
                    # 尝试计算相对于STORAGE_PATH的路径
                    if [[ "$full_path" == "$STORAGE_PATH"* ]]; then
                        rel_path="${full_path#$STORAGE_PATH/}"
                    else
                        rel_path="$plugin_path"
                    fi
                else
                    print_message "$RED" "指定的插件路径不存在: $plugin_path"
                    continue
                fi
            fi
            
            print_message "$GREEN" "同步指定插件: $full_path"
            
            # 调用API同步插件
            response=$(curl -s -X POST "http://localhost:$APP_PORT/api/v1/plugins/sync" \
                -H "Content-Type: application/json" \
                -d "{\"pluginDirPath\": \"$rel_path\", \"updateIfNewer\": true}")
            
            # 检查响应
            if echo "$response" | grep -q "\"success\":true"; then
                print_message "$GREEN" "插件 $rel_path 同步成功"
            else
                print_message "$RED" "插件 $rel_path 同步失败: $response"
            fi
        done
    fi
    
    print_message "$GREEN" "插件同步完成"
}

# 显示使用方法
show_usage() {
    echo "使用方法: $0 {start|stop|restart|status|sync|celery-*|start-all|stop-all} [参数]"
    echo ""
    echo "基础命令:"
    echo "  start       - 启动FastAPI服务"
    echo "  stop        - 停止FastAPI服务"
    echo "  restart     - 重启FastAPI服务"
    echo "  status      - 查看FastAPI服务状态"
    echo "  sync        - 同步插件"
    echo ""
    echo "Celery命令:"
    echo "  celery-worker  - 启动Celery Worker"
    echo "  celery-flower  - 启动Celery Flower监控"
    echo "  celery-stop    - 停止所有Celery服务"
    echo "  start-all      - 启动完整服务 (FastAPI + Celery)"
    echo "  stop-all       - 停止所有服务"
    echo ""
    echo "参数说明:"
    echo "  env     - start/restart/start-all可选参数，保留兼容性，实际统一使用8个workers"
    echo ""
    echo "  plugin_path - sync可选参数，指定要同步的插件路径"
    echo "                不提供参数时同步所有插件的最新版本"
    echo "                例如：$0 sync ai-platform-plugin/face_detect_mosaic/2.0.2"
    echo ""
    echo "示例:"
    echo "  $0 start-all prod     # 启动完整服务(生产环境)"
    echo "  $0 celery-worker      # 只启动Celery Worker"
    echo "  $0 stop-all           # 停止所有服务"
}

# 加载环境变量
export LD_LIBRARY_PATH="/home/<USER>/anaconda3/envs/ai_platform/lib:/usr/local/cuda/lib64:/usr/local/cuda-12.4/lib64"

# Celery相关功能
start_celery_worker() {
    print_message "$GREEN" "正在启动 Celery Worker..."

    # 激活 conda 环境
    source /home/<USER>/anaconda3/bin/activate $CONDA_ENV

    # 设置日志文件
    local log_file="./run/celery_worker.log"

    # 启动Celery Worker
    nohup celery -A app.core.celery_app worker \
        --loglevel=info \
        --concurrency=4 \
        --queues=download,processing,management \
        --max-tasks-per-child=1000 \
        --prefetch-multiplier=1 \
        --pidfile=./run/celery_worker.pid \
        --logfile="$log_file" > /dev/null 2>&1 &

    print_message "$GREEN" "Celery Worker 已启动"
}

start_celery_flower() {
    print_message "$GREEN" "正在启动 Celery Flower..."

    # 激活 conda 环境
    source /home/<USER>/anaconda3/bin/activate $CONDA_ENV

    # 设置日志文件
    local log_file="./run/celery_flower.log"

    # 启动Celery Flower
    nohup celery -A app.core.celery_app flower \
        --port=5555 \
        --broker=${REDIS_URL:-redis://localhost:6379/1} \
        --pidfile=./run/celery_flower.pid \
        --logfile="$log_file" > /dev/null 2>&1 &

    print_message "$GREEN" "Celery Flower 已启动 (端口: 5555)"
}

stop_celery() {
    print_message "$GREEN" "正在停止 Celery 服务..."

    # 停止Celery Worker
    if [ -f "./run/celery_worker.pid" ]; then
        local worker_pid=$(cat "./run/celery_worker.pid")
        if ps -p $worker_pid > /dev/null; then
            kill -TERM $worker_pid
            print_message "$GREEN" "Celery Worker 已停止"
        fi
        rm -f "./run/celery_worker.pid"
    fi

    # 停止Celery Flower
    if [ -f "./run/celery_flower.pid" ]; then
        local flower_pid=$(cat "./run/celery_flower.pid")
        if ps -p $flower_pid > /dev/null; then
            kill -TERM $flower_pid
            print_message "$GREEN" "Celery Flower 已停止"
        fi
        rm -f "./run/celery_flower.pid"
    fi
}

start_all() {
    local env=${2:-"prod"}

    print_message "$GREEN" "正在启动完整服务 (FastAPI + Celery)..."

    # 启动Celery Worker
    start_celery_worker

    # 启动Celery Flower
    start_celery_flower

    # 等待Celery服务启动
    sleep 3

    # 启动FastAPI应用
    start $1 $env
}

# 主程序
case "$1" in
    start)
        start $@
        ;;
    stop)
        stop
        ;;
    restart)
        restart $@
        ;;
    status)
        check_status
        ;;
    sync)
        shift  # 移除第一个参数
        sync $@
        ;;
    celery-worker)
        start_celery_worker
        ;;
    celery-flower)
        start_celery_flower
        ;;
    celery-stop)
        stop_celery
        ;;
    start-all)
        start_all $@
        ;;
    stop-all)
        stop
        stop_celery
        ;;
    *)
        show_usage
        exit 1
        ;;
esac

exit 0
