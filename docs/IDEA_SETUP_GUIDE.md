# IntelliJ IDEA 启动配置指南

## 🚀 快速开始

### 1. 项目导入
1. 打开 IntelliJ IDEA
2. 选择 `File` → `Open`
3. 选择项目根目录 `/Users/<USER>/Codes/inskylab/ai-platform`
4. 等待项目索引完成

### 2. Python解释器配置
1. 打开 `File` → `Settings` (Windows/Linux) 或 `IntelliJ IDEA` → `Preferences` (macOS)
2. 导航到 `Project: ai-platform` → `Python Interpreter`
3. 选择正确的Python解释器 (建议Python 3.11+)
4. 确保已安装所有依赖包

### 3. 环境变量配置
1. 复制 `.env.example` 为 `.env`
2. 填入实际的配置值 (数据库、Redis、OSS等)
3. 确保所有必需的环境变量都已配置

## 🎯 运行配置

IDEA已为你预配置了以下运行配置：

### 开发环境启动
- **配置名**: `FastAPI Development`
- **描述**: 开发模式启动，支持热重载
- **端口**: 8000
- **特点**: DEBUG=True, 自动重载代码变更

### 生产环境启动  
- **配置名**: `FastAPI Production`
- **描述**: 生产模式启动，使用Gunicorn
- **端口**: 8000
- **特点**: 8个workers, 高性能

### Celery Worker
- **配置名**: `Celery Worker`
- **描述**: 启动Celery后台任务处理器
- **队列**: download, processing, management
- **并发**: 4个worker进程

### Celery监控
- **配置名**: `Celery Flower`
- **描述**: 启动Celery监控界面
- **端口**: 5555
- **访问**: http://localhost:5555

### 完整服务
- **配置名**: `All Services`
- **描述**: 同时启动FastAPI + Celery Worker + Flower
- **推荐**: 完整功能测试使用

## 📋 启动步骤

### 方式一：完整服务启动 (推荐)
1. 确保Redis服务运行
2. 确保PostgreSQL数据库可访问
3. 在IDEA中选择 `All Services` 配置
4. 点击绿色运行按钮 ▶️

### 方式二：分步启动
1. **启动FastAPI**: 选择 `FastAPI Development` 并运行
2. **启动Celery Worker**: 选择 `Celery Worker` 并运行  
3. **启动监控** (可选): 选择 `Celery Flower` 并运行

## 🔧 配置详情

### FastAPI Development 配置
```xml
模块: ai-platform
工作目录: $PROJECT_DIR$
脚本: uvicorn (模块模式)
参数: main:app --host 0.0.0.0 --port 8000 --reload
环境变量:
  - PYTHONPATH=$PROJECT_DIR$
  - DEBUG=True
```

### Celery Worker 配置
```xml
模块: ai-platform  
工作目录: $PROJECT_DIR$
脚本: celery (模块模式)
参数: -A app.core.celery_app worker --loglevel=info --concurrency=4 --queues=download,processing,management --max-tasks-per-child=1000 --prefetch-multiplier=1
环境变量:
  - PYTHONPATH=$PROJECT_DIR$
```

## 🐛 调试配置

### 断点调试
1. 在代码中设置断点
2. 选择对应的运行配置
3. 点击调试按钮 🐛 而不是运行按钮 ▶️

### 日志查看
- **FastAPI日志**: 在IDEA的Run窗口查看
- **Celery日志**: 在对应的Celery Worker窗口查看
- **文件日志**: 查看 `logs/` 目录下的日志文件

## 📊 监控和验证

### 服务状态检查
1. **FastAPI**: 访问 http://localhost:8000/docs
2. **健康检查**: 访问 http://localhost:8000/api/v1/system/health
3. **Celery监控**: 访问 http://localhost:5555
4. **Celery健康**: 访问 http://localhost:8000/api/v1/monitoring/celery/health

### API测试
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

## ⚠️ 常见问题

### 1. 模块导入错误
**问题**: `ModuleNotFoundError: No module named 'app'`
**解决**: 
- 检查PYTHONPATH是否设置为项目根目录
- 确保项目根目录被标记为源码根目录

### 2. 依赖包缺失
**问题**: `ImportError: No module named 'celery'`
**解决**:
```bash
pip install -r requirements.txt
```

### 3. Redis连接失败
**问题**: `ConnectionError: Error connecting to Redis`
**解决**:
- 确保Redis服务运行: `redis-server`
- 检查REDIS_URL配置是否正确

### 4. 数据库连接失败
**问题**: `OperationalError: could not connect to server`
**解决**:
- 确保PostgreSQL服务运行
- 检查DATABASE_URL配置
- 执行数据库迁移: `psql -f migrations/add_celery_fields.sql`

### 5. 端口占用
**问题**: `OSError: [Errno 48] Address already in use`
**解决**:
- 检查端口占用: `lsof -i :8000`
- 杀死占用进程或更改端口

## 🔄 热重载配置

### 开发模式特性
- **代码热重载**: 修改Python文件自动重启
- **模板热重载**: 修改模板文件自动更新
- **静态文件**: 修改CSS/JS自动刷新

### 排除文件
以下文件/目录的修改不会触发重载:
- `logs/`
- `storage/`
- `__pycache__/`
- `*.pyc`

## 📝 开发建议

### 代码风格
- 使用Black格式化代码
- 遵循PEP 8规范
- 添加类型注解

### 测试运行
```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_image_metadata.py

# 生成覆盖率报告
pytest --cov=app tests/
```

### Git集成
- 使用IDEA内置的Git工具
- 提交前运行测试
- 遵循约定式提交规范

---

**配置完成后，你就可以在IDEA中方便地启动和调试AI平台项目了！** 🎉

如有问题，请查看项目文档或联系开发团队。
