# Celery迁移快速开始指南

## 🚀 5分钟快速体验

### 1. 安装依赖
```bash
pip install celery[redis]==5.3.4 flower==2.0.1
```

### 2. 创建基础配置
```python
# app/core/celery_app.py
from celery import Celery
from app.config.settings import settings

celery_app = Celery(
    "ai-platform",
    broker=settings.REDIS_URL,
    backend=settings.REDIS_URL
)

# 基础配置
celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    task_acks_late=True,
    worker_prefetch_multiplier=1
)
```

### 3. 创建下载任务
```python
# app/core/tasks/download.py
from app.core.celery_app import celery_app
from app.core.file.manager import file_manager
import httpx

@celery_app.task(
    bind=True,
    autoretry_for=(httpx.HTTPStatusError, httpx.ConnectError, httpx.TimeoutException),
    retry_kwargs={'max_retries': 5},
    retry_backoff=True,
    retry_backoff_max=300
)
def download_file_task(self, url: str, file_type: str = None):
    """文件下载任务 - 自动重试"""
    try:
        file = file_manager.download_from_url(
            url=url,
            file_type=file_type,
            save_to_storage=True
        )
        return {
            'file_id': file.id,
            'file_hash': file.file_hash,
            'status': 'success'
        }
    except Exception as exc:
        print(f"Download failed: {exc}, will retry...")
        raise
```

### 4. 启动Worker
```bash
# 启动Celery Worker
celery -A app.core.celery_app worker --loglevel=info

# 启动Flower监控 (可选)
celery -A app.core.celery_app flower --port=5555
```

### 5. 测试任务

```python
# 测试脚本
from app.core.celery.download import download_file_task

# 提交任务
result = download_file_task.delay('http://example.com/test.jpg')

# 检查状态
print(f"Task ID: {result.id}")
print(f"Status: {result.status}")

# 获取结果 (阻塞等待)
try:
    file_info = result.get(timeout=60)
    print(f"Download success: {file_info}")
except Exception as e:
    print(f"Download failed: {e}")
```

## 🔄 渐进式迁移步骤

### 步骤1: 并行运行 (第1周)

```python
# app/core/task/manager.py - 修改现有管理器
class TaskManager:
    def __init__(self):
        self.use_celery = settings.USE_CELERY_FOR_TASKS  # 默认False

    async def create_task(self, task_in: TaskCreate):
        if self.use_celery:
            return await self._create_celery_task(task_in)
        else:
            return await self._create_legacy_task(task_in)  # 原有逻辑

    async def _create_celery_task(self, task_in: TaskCreate):
        """使用Celery创建任务"""
        from app.core.celery.download import download_file_task

        # 创建数据库记录
        task = await task_crud.create(db, obj_in={
            "total_items": len(task_in.items),
            "status": "pending"
        })

        # 提交Celery任务
        celery_jobs = []
        for item in task_in.items:
            job = download_file_task.delay(item.file_url, item.file_type)
            celery_jobs.append(job.id)

        # 保存Celery任务ID
        await task_crud.update(db, db_obj=task, obj_in={
            "celery_task_ids": celery_jobs,
            "status": "running"
        })

        return task
```

### 步骤2: 小流量测试 (第2周)
```python
# app/config/settings.py
class Settings(BaseSettings):
    USE_CELERY_FOR_TASKS: bool = True  # 开启Celery
    CELERY_MIGRATION_PERCENTAGE: int = 10  # 10%流量使用Celery
```

### 步骤3: 逐步扩大 (第3-4周)
```python
# 逐步提高百分比
CELERY_MIGRATION_PERCENTAGE: int = 50  # 第3周: 50%
CELERY_MIGRATION_PERCENTAGE: int = 100  # 第4周: 100%
```

### 步骤4: 清理旧代码 (第5周)
```python
# 移除旧的任务调度器代码
# 删除Redis队列相关代码
# 简化TaskManager
```

## 📊 关键监控指标

### 1. 任务成功率对比
```python
# 监控脚本
def compare_success_rates():
    # 旧系统成功率
    legacy_success_rate = get_legacy_success_rate()
    
    # Celery成功率  
    celery_success_rate = get_celery_success_rate()
    
    print(f"Legacy: {legacy_success_rate:.2%}")
    print(f"Celery: {celery_success_rate:.2%}")
    
    if celery_success_rate < legacy_success_rate * 0.95:
        print("⚠️  Celery成功率低于预期，需要调查")
    else:
        print("✅ Celery成功率正常")
```

### 2. 性能对比
```python
def compare_performance():
    # 平均任务完成时间
    legacy_avg_time = get_legacy_avg_completion_time()
    celery_avg_time = get_celery_avg_completion_time()
    
    improvement = (legacy_avg_time - celery_avg_time) / legacy_avg_time
    print(f"性能提升: {improvement:.2%}")
```

## 🚨 回滚计划

### 紧急回滚 (5分钟内)
```python
# app/config/settings.py
USE_CELERY_FOR_TASKS: bool = False  # 立即关闭Celery
```

### 数据一致性检查
```python
# 回滚后检查脚本
async def check_data_consistency():
    # 检查是否有Celery任务仍在运行
    inspect = celery_app.control.inspect()
    active_tasks = inspect.active()
    
    if active_tasks:
        print(f"⚠️  仍有 {len(active_tasks)} 个Celery任务在运行")
        
        # 等待任务完成或强制终止
        for worker, tasks in active_tasks.items():
            for task in tasks:
                print(f"Task {task['id']} still running")
    
    # 检查数据库状态一致性
    inconsistent_tasks = await find_inconsistent_task_status()
    if inconsistent_tasks:
        print(f"发现 {len(inconsistent_tasks)} 个状态不一致的任务")
        await fix_inconsistent_tasks(inconsistent_tasks)
```

## 🔧 常见问题解决

### 1. Worker启动失败
```bash
# 检查Redis连接
redis-cli -u $REDIS_URL ping

# 检查Python路径
python -c "from app.core.celery_app import celery_app; print('OK')"

# 检查权限
ls -la storage/
```

### 2. 任务一直Pending
```bash
# 检查队列
celery -A app.core.celery_app inspect active_queues

# 检查Worker状态
celery -A app.core.celery_app status

# 清空队列 (谨慎使用)
celery -A app.core.celery_app purge
```

### 3. 内存使用过高
```python
# 限制Worker进程生命周期
CELERY_WORKER_MAX_TASKS_PER_CHILD = 1000

# 启动时设置
celery -A app.core.celery_app worker --max-tasks-per-child=1000
```

## 📋 检查清单

### 迁移前检查
- [ ] Redis服务正常运行
- [ ] 存储目录权限正确
- [ ] 数据库连接正常
- [ ] 原有任务队列为空

### 迁移中检查  
- [ ] Celery Worker正常启动
- [ ] 任务能够正常提交
- [ ] 文件下载成功率正常
- [ ] 数据库状态同步正确

### 迁移后检查
- [ ] 所有任务类型都能正常处理
- [ ] 错误重试机制工作正常
- [ ] 监控指标显示正常
- [ ] 旧系统代码已清理

## 🎯 成功标准

### 功能标准
- 任务成功率 ≥ 95%
- 文件下载重试成功率 ≥ 80%
- 任务状态同步准确率 = 100%

### 性能标准  
- 任务处理吞吐量提升 ≥ 30%
- 平均任务完成时间减少 ≥ 20%
- 系统资源利用率优化 ≥ 15%

### 运维标准
- Worker自动重启成功率 = 100%
- 监控告警及时性 ≤ 1分钟
- 故障恢复时间 ≤ 5分钟

---

**快速开始指南版本**: v1.0  
**适用于**: 开发和测试环境快速验证  
**生产环境**: 请参考完整的迁移指南
