# AI平台任务系统Celery迁移指南

## 📋 项目背景

### 当前问题分析

通过对 `logs/app.2025-07-02_10-06-33_492199.log` 的分析，发现任务"取消"的根本原因是：

1. **OSS签名URL过期**：大量 `403 Forbidden` 错误
2. **文件下载失败**：导致任务项无法正常创建
3. **任务状态卡死**：任务长期处于 `initializing` 状态
4. **重试机制不足**：虽然有STS重试，但缺乏完整的重试策略

### 现有重试逻辑评估

✅ **已有的重试机制**：
- OSS 403错误的STS令牌重试
- HTTP客户端超时设置（60秒）

❌ **缺失的重试机制**：
- 任务级别重试
- 指数退避策略
- 网络错误重试
- 任务超时自动恢复

## 🎯 迁移目标

1. **提高任务可靠性**：自动重试、错误恢复
2. **增强可观测性**：实时监控、详细日志
3. **支持水平扩展**：分布式任务队列
4. **简化运维管理**：标准化工具链

## 🏗️ 技术架构设计

### Celery架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   FastAPI App   │───▶│   Redis Broker  │───▶│  Celery Worker  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │  Redis Backend  │    │     Flower      │
│   (任务状态)     │    │   (任务结果)     │    │    (监控)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 任务流程设计

```
任务创建 → 文件下载(并行) → 任务处理(并行) → 结果汇总
   │            │                │              │
   ▼            ▼                ▼              ▼
数据库记录   Celery Group    Celery Chord    状态更新
```

## 📅 实施计划

### 阶段一：基础环境搭建（1-2周）

#### 1.1 依赖安装
```bash
pip install celery[redis]==5.3.4
pip install flower==2.0.1
```

#### 1.2 Celery配置文件
创建 `app/core/celery_app.py`：

```python
from celery import Celery
from app.config.settings import settings

celery_app = Celery(
    "ai-platform",
    broker=settings.REDIS_URL,
    backend=settings.REDIS_URL,
    include=['app.core.celery']
)

# 基础配置
celery_app.conf.update(
    # 序列化配置
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='Asia/Shanghai',
    
    # 可靠性配置
    task_acks_late=True,
    task_reject_on_worker_lost=True,
    worker_prefetch_multiplier=1,
    
    # 重试配置
    result_backend_always_retry=True,
    result_backend_max_retries=10,
    
    # 队列路由
    task_routes={
        'app.core.celery.download_file': {'queue': 'download'},
        'app.core.celery.process_task_item': {'queue': 'processing'},
        'app.core.celery.cleanup_task': {'queue': 'cleanup'},
    }
)
```

#### 1.3 任务定义
创建 `app/core/tasks/__init__.py`：

```python
from .download_tasks import download_file_task
from .processing_tasks import process_task_item
from .management_tasks import cleanup_failed_tasks

__all__ = [
    'download_file_task',
    'process_task_item', 
    'cleanup_failed_tasks'
]
```

### 阶段二：核心任务实现（2-3周）

#### 2.1 文件下载任务
创建 `app/core/tasks/download_tasks.py`：

```python
from celery import Task
from celery.exceptions import Retry
from app.core.celery_app import celery_app
from app.core.file.manager import file_manager
import httpx
import logging

logger = logging.getLogger(__name__)

class DownloadTask(Task):
    """文件下载任务基类"""
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        logger.error(f"Download task {task_id} failed: {exc}")
        # 更新数据库状态
    
    def on_retry(self, exc, task_id, args, kwargs, einfo):
        logger.warning(f"Download task {task_id} retrying: {exc}")

@celery_app.task(
    bind=True,
    base=DownloadTask,
    autoretry_for=(
        httpx.HTTPStatusError,
        httpx.ConnectError,
        httpx.TimeoutException,
        OSError
    ),
    retry_kwargs={'max_retries': 5},
    retry_backoff=True,
    retry_backoff_max=300,
    retry_jitter=True
)
def download_file_task(self, url: str, file_type: str = None):
    """
    文件下载任务
    
    Args:
        url: 文件URL
        file_type: 文件类型
        
    Returns:
        dict: 下载结果
    """
    try:
        file = file_manager.download_from_url(
            url=url,
            file_type=file_type,
            save_to_storage=True
        )
        
        return {
            'file_id': file.id,
            'file_hash': file.file_hash,
            'local_path': file.local_path,
            'status': 'success'
        }
        
    except httpx.HTTPStatusError as exc:
        if exc.response.status_code == 403:
            logger.warning(f"403 error for {url}, will retry with STS")
        raise  # 让Celery处理重试
        
    except Exception as exc:
        logger.error(f"Unexpected error downloading {url}: {exc}")
        raise
```

#### 2.2 任务处理逻辑
创建 `app/core/tasks/processing_tasks.py`：

```python
@celery_app.task(
    bind=True,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 3},
    retry_backoff=True,
    retry_backoff_max=120
)
def process_task_item(self, file_id: int, plugin_code: str, plugin_version: str, params: dict = None):
    """
    处理单个任务项
    
    Args:
        file_id: 文件ID
        plugin_code: 插件代码
        plugin_version: 插件版本
        params: 处理参数
        
    Returns:
        dict: 处理结果
    """
    try:
        # 获取文件信息
        file_info = get_file_by_id(file_id)
        if not file_info:
            raise ValueError(f"File {file_id} not found")
        
        # 获取插件实例
        plugin = get_plugin_instance(plugin_code, plugin_version)
        if not plugin:
            raise ValueError(f"Plugin {plugin_code}:{plugin_version} not found")
        
        # 执行处理
        result = plugin.process(file_info.local_path, params or {})
        
        return {
            'file_id': file_id,
            'plugin_code': plugin_code,
            'result': result,
            'status': 'completed'
        }
        
    except Exception as exc:
        logger.error(f"Processing failed for file {file_id}: {exc}")
        raise
```

### 阶段三：任务管理器重构（2周）

#### 3.1 Celery任务管理器
创建 `app/core/task/celery_manager.py`：

```python
from celery import group, chord
from app.core.celery import download_file_task, process_task_item
from app.core.celery_app import celery_app


class CeleryTaskManager:
   """基于Celery的任务管理器"""

   async def create_task(self, task_in: TaskCreate) -> TaskResponse:
      """创建任务"""
      # 1. 创建数据库记录
      task = await task_crud.create(db, obj_in={
         "total_items": len(task_in.items) * len(task_in.plugins),
         "status": "pending",
         "priority": task_in.priority
      })

      # 2. 创建下载任务组
      download_jobs = []
      for item in task_in.items:
         download_job = download_file_task.s(
            url=item.file_url,
            file_type=item.file_type
         )
         download_jobs.append(download_job)

      # 3. 创建处理回调
      processing_callback = self.create_processing_workflow.s(
         task_id=task.id,
         plugins=[p.dict() for p in task_in.plugins]
      )

      # 4. 使用chord模式：下载完成后处理
      workflow = chord(group(download_jobs))(processing_callback)

      # 5. 保存workflow信息
      await task_crud.update(db, db_obj=task, obj_in={
         "celery_task_id": workflow.id,
         "status": "running"
      })

      return await self.get_task_progress(db, task=task)

   @celery_app.task(bind=True)
   def create_processing_workflow(self, download_results, task_id, plugins):
      """创建处理工作流"""
      processing_jobs = []

      for result in download_results:
         if result.get('status') == 'success':
            file_id = result['file_id']

            for plugin in plugins:
               job = process_task_item.s(
                  file_id=file_id,
                  plugin_code=plugin['plugin_code'],
                  plugin_version=plugin['plugin_version'],
                  params=plugin.get('params', {})
               )
               processing_jobs.append(job)

      # 并行处理所有任务项
      if processing_jobs:
         processing_group = group(processing_jobs)
         return processing_group.apply_async()

      return {'status': 'no_valid_files'}
```

### 阶段四：监控和运维（1周）

#### 4.1 Flower监控部署
```bash
# 启动Flower
celery -A app.core.celery_app flower --port=5555 --broker=redis://localhost:6379/1
```

#### 4.2 健康检查接口
创建 `app/api/v1/celery_health.py`：

```python
from fastapi import APIRouter
from app.core.celery_app import celery_app

router = APIRouter()

@router.get("/celery/health")
async def celery_health():
    """Celery健康检查"""
    try:
        # 检查worker状态
        inspect = celery_app.control.inspect()
        stats = inspect.stats()
        active = inspect.active()
        
        worker_count = len(stats) if stats else 0
        active_tasks = sum(len(tasks) for tasks in active.values()) if active else 0
        
        return {
            "status": "healthy" if worker_count > 0 else "no_workers",
            "workers": worker_count,
            "active_tasks": active_tasks,
            "queues": list(celery_app.conf.task_routes.values())
        }
    except Exception as e:
        return {
            "status": "unhealthy", 
            "error": str(e)
        }

@router.get("/celery/tasks/{task_id}")
async def get_celery_task_status(task_id: str):
    """获取Celery任务状态"""
    result = celery_app.AsyncResult(task_id)
    return {
        "task_id": task_id,
        "status": result.status,
        "result": result.result,
        "traceback": result.traceback
    }
```

## 🔄 渐进式迁移策略

### 混合模式运行
创建 `app/core/task/hybrid_manager.py`：

```python
class HybridTaskManager:
    """混合任务管理器"""
    
    def __init__(self):
        self.use_celery = settings.USE_CELERY_FOR_TASKS
        self.migration_percentage = settings.CELERY_MIGRATION_PERCENTAGE
        self.celery_manager = CeleryTaskManager()
        self.legacy_manager = TaskManager()
    
    async def create_task(self, task_in: TaskCreate):
        # 根据配置决定使用哪个管理器
        if self.use_celery:
            if self.migration_percentage == 100:
                return await self.celery_manager.create_task(task_in)
            elif self.should_use_celery():
                return await self.celery_manager.create_task(task_in)
        
        return await self.legacy_manager.create_task(task_in)
    
    def should_use_celery(self) -> bool:
        """根据百分比决定是否使用Celery"""
        import random
        return random.randint(1, 100) <= self.migration_percentage
```

## ⚙️ 配置更新

### settings.py 新增配置
```python
class Settings(BaseSettings):
    # Celery配置
    USE_CELERY_FOR_TASKS: bool = False
    CELERY_MIGRATION_PERCENTAGE: int = 0  # 0-100
    
    # Celery Broker配置
    CELERY_BROKER_URL: str = REDIS_URL
    CELERY_RESULT_BACKEND: str = REDIS_URL
    
    # Worker配置
    CELERY_WORKER_CONCURRENCY: int = 4
    CELERY_WORKER_MAX_TASKS_PER_CHILD: int = 1000
    
    # 任务配置
    CELERY_TASK_TIME_LIMIT: int = 300  # 5分钟
    CELERY_TASK_SOFT_TIME_LIMIT: int = 240  # 4分钟
```

## 🚀 部署指南

### Docker Compose 配置
```yaml
version: '3.8'
services:
  celery-worker:
    build: .
    command: celery -A app.core.celery_app worker --loglevel=info --concurrency=4
    volumes:
      - ./storage:/app/storage
    environment:
      - REDIS_URL=redis://redis:6379/1
      - DATABASE_URL=************************************/db
    depends_on:
      - redis
      - postgres
  
  celery-flower:
    build: .
    command: celery -A app.core.celery_app flower --port=5555
    ports:
      - "5555:5555"
    environment:
      - REDIS_URL=redis://redis:6379/1
    depends_on:
      - redis
  
  celery-beat:
    build: .
    command: celery -A app.core.celery_app beat --loglevel=info
    environment:
      - REDIS_URL=redis://redis:6379/1
    depends_on:
      - redis
```

### 启动脚本
```bash
#!/bin/bash
# start_celery.sh

# 启动Celery Worker
celery -A app.core.celery_app worker \
  --loglevel=info \
  --concurrency=4 \
  --queues=download,processing,cleanup \
  --max-tasks-per-child=1000 &

# 启动Flower监控
celery -A app.core.celery_app flower --port=5555 &

# 启动Beat调度器（如果需要定时任务）
celery -A app.core.celery_app beat --loglevel=info &

wait
```

## 📊 迁移时间表

| 阶段 | 时间 | 主要工作 | 交付物 | 负责人 |
|------|------|----------|--------|--------|
| 阶段一 | 1-2周 | 环境搭建、基础配置 | Celery配置、任务定义 | 后端团队 |
| 阶段二 | 2-3周 | 核心任务实现 | 下载任务、处理任务 | 后端团队 |
| 阶段三 | 2周 | 任务管理器重构 | CeleryTaskManager | 后端团队 |
| 阶段四 | 1周 | 监控运维 | Flower、健康检查 | 运维团队 |
| 阶段五 | 2-3周 | 渐进式迁移 | 混合模式、完全切换 | 全团队 |

## ✅ 验收标准

### 功能验收
- [ ] 文件下载任务自动重试
- [ ] 任务状态实时更新
- [ ] 错误任务自动恢复
- [ ] 任务进度准确显示
- [ ] 并发处理能力提升

### 性能验收
- [ ] 任务处理吞吐量提升30%
- [ ] 任务失败率降低到5%以下
- [ ] 平均任务完成时间减少20%
- [ ] 系统资源利用率优化

### 运维验收
- [ ] Flower监控正常运行
- [ ] 健康检查接口可用
- [ ] 日志记录完整清晰
- [ ] 告警机制正常工作

## 🔧 故障排查

### 常见问题及解决方案

1. **Worker无法启动**
   ```bash
   # 检查Redis连接
   redis-cli -u $REDIS_URL ping
   
   # 检查权限
   ls -la /app/storage
   ```

2. **任务一直Pending**
   ```bash
   # 检查队列状态
   celery -A app.core.celery_app inspect active_queues
   
   # 检查Worker状态
   celery -A app.core.celery_app status
   ```

3. **内存泄漏**
   ```python
   # 设置max_tasks_per_child
   CELERY_WORKER_MAX_TASKS_PER_CHILD = 1000
   ```

## 📈 监控指标

### 关键指标
- 任务成功率
- 任务平均执行时间
- 队列长度
- Worker CPU/内存使用率
- 重试次数统计

### 告警规则
- 任务失败率 > 10%
- 队列积压 > 1000
- Worker内存使用 > 80%
- 任务平均执行时间 > 5分钟

## 🔍 技术实现细节

### 错误处理策略

#### 1. 分层错误处理
```python
# app/core/tasks/error_handlers.py
from celery.exceptions import Retry, Ignore
from app.core.celery_app import celery_app

@celery_app.task
def handle_download_error(task_id: str, exc_info: dict, file_url: str):
    """处理下载错误"""
    error_type = exc_info.get('type')

    if error_type == 'HTTPStatusError':
        status_code = exc_info.get('status_code')
        if status_code == 403:
            # OSS权限问题，需要人工介入
            send_alert(f"OSS权限问题: {file_url}")
        elif status_code >= 500:
            # 服务器错误，可以重试
            return "retry"

    elif error_type in ['ConnectError', 'TimeoutException']:
        # 网络问题，可以重试
        return "retry"

    # 其他错误标记为失败
    return "failed"

@celery_app.task
def handle_processing_error(task_id: str, exc_info: dict, plugin_info: dict):
    """处理任务项错误"""
    # 根据插件类型决定错误处理策略
    plugin_code = plugin_info.get('plugin_code')

    if plugin_code in ['critical_plugin_1', 'critical_plugin_2']:
        # 关键插件失败需要告警
        send_critical_alert(f"关键插件失败: {plugin_code}")

    # 记录错误统计
    increment_error_counter(plugin_code, exc_info.get('type'))
```

#### 2. 自定义重试策略
```python
# app/core/tasks/retry_strategies.py
from celery.exceptions import Retry
import random

def exponential_backoff_with_jitter(attempt: int, base_delay: int = 2, max_delay: int = 300):
    """指数退避 + 随机抖动"""
    delay = min(base_delay ** attempt, max_delay)
    jitter = random.uniform(0.5, 1.5)
    return delay * jitter

def linear_backoff(attempt: int, base_delay: int = 10, max_delay: int = 300):
    """线性退避"""
    return min(base_delay * attempt, max_delay)

class SmartRetryMixin:
    """智能重试混入类"""

    def smart_retry(self, exc, max_retries=5, strategy='exponential'):
        """智能重试"""
        if self.request.retries >= max_retries:
            raise exc

        if strategy == 'exponential':
            countdown = exponential_backoff_with_jitter(self.request.retries)
        else:
            countdown = linear_backoff(self.request.retries)

        raise self.retry(exc=exc, countdown=countdown, max_retries=max_retries)
```

### 任务状态同步

#### 1. 状态映射和同步
```python
# app/core/task/status_sync.py
from celery.signals import task_prerun, task_postrun, task_failure, task_retry
from app.core.crud.task import task_crud
from app.db.session import get_async_db

CELERY_TO_DB_STATUS = {
    'PENDING': 'pending',
    'STARTED': 'running',
    'SUCCESS': 'completed',
    'FAILURE': 'failed',
    'RETRY': 'retrying',
    'REVOKED': 'canceled'
}

@task_prerun.connect
def task_prerun_handler(sender=None, task_id=None, task=None, args=None, kwargs=None, **kwds):
    """任务开始前的处理"""
    if hasattr(task, 'db_task_id'):
        # 更新数据库状态为运行中
        update_task_status_async(task.db_task_id, 'running')

@task_postrun.connect
def task_postrun_handler(sender=None, task_id=None, task=None, args=None, kwargs=None, retval=None, state=None, **kwds):
    """任务完成后的处理"""
    if hasattr(task, 'db_task_id'):
        db_status = CELERY_TO_DB_STATUS.get(state, 'unknown')
        update_task_status_async(task.db_task_id, db_status)

@task_failure.connect
def task_failure_handler(sender=None, task_id=None, exception=None, traceback=None, einfo=None, **kwds):
    """任务失败处理"""
    if hasattr(sender, 'db_task_id'):
        update_task_status_async(sender.db_task_id, 'failed', str(exception))

@task_retry.connect
def task_retry_handler(sender=None, task_id=None, reason=None, einfo=None, **kwds):
    """任务重试处理"""
    if hasattr(sender, 'db_task_id'):
        update_task_status_async(sender.db_task_id, 'retrying', str(reason))

def update_task_status_async(db_task_id: int, status: str, error: str = None):
    """异步更新任务状态"""
    import asyncio
    asyncio.create_task(update_task_status(db_task_id, status, error))

async def update_task_status(db_task_id: int, status: str, error: str = None):
    """更新任务状态"""
    async with get_async_db() as db:
        update_data = {"status": status}
        if error:
            update_data["error"] = error
        if status in ['completed', 'failed', 'canceled']:
            update_data["completed_at"] = datetime.now()

        await task_crud.update_by_id(db, id=db_task_id, obj_in=update_data)
```

### 性能优化

#### 1. 批量操作优化
```python
# app/core/tasks/batch_tasks.py
from celery import group, chord
from app.core.celery_app import celery_app

@celery_app.task
def batch_download_files(urls: List[str], batch_size: int = 10):
    """批量下载文件"""
    # 分批处理，避免创建过多并发任务
    batches = [urls[i:i+batch_size] for i in range(0, len(urls), batch_size)]

    batch_jobs = []
    for batch in batches:
        batch_job = group(download_file_task.s(url) for url in batch)
        batch_jobs.append(batch_job)

    # 串行执行批次，并行执行批次内任务
    return group(batch_jobs).apply_async()

@celery_app.task
def batch_process_files(file_ids: List[int], plugin_configs: List[dict]):
    """批量处理文件"""
    # 使用chord模式：所有处理完成后汇总结果
    processing_jobs = []

    for file_id in file_ids:
        for plugin_config in plugin_configs:
            job = process_task_item.s(
                file_id=file_id,
                plugin_code=plugin_config['plugin_code'],
                plugin_version=plugin_config['plugin_version'],
                params=plugin_config.get('params', {})
            )
            processing_jobs.append(job)

    # 处理完成后汇总结果
    callback = aggregate_results.s()
    return chord(processing_jobs)(callback)

@celery_app.task
def aggregate_results(results: List[dict]):
    """汇总处理结果"""
    success_count = sum(1 for r in results if r.get('status') == 'completed')
    failed_count = len(results) - success_count

    return {
        'total': len(results),
        'success': success_count,
        'failed': failed_count,
        'success_rate': success_count / len(results) if results else 0
    }
```

#### 2. 内存优化
```python
# app/core/tasks/memory_optimized.py
import gc
from contextlib import contextmanager

@contextmanager
def memory_cleanup():
    """内存清理上下文管理器"""
    try:
        yield
    finally:
        gc.collect()

@celery_app.task(bind=True)
def memory_optimized_task(self, large_data):
    """内存优化的任务"""
    with memory_cleanup():
        # 处理大数据
        result = process_large_data(large_data)

        # 清理中间变量
        del large_data

        return result
```

### 监控和告警

#### 1. 自定义监控指标
```python
# app/core/monitoring/metrics.py
from prometheus_client import Counter, Histogram, Gauge
import time

# 定义指标
TASK_COUNTER = Counter('celery_tasks_total', 'Total number of tasks', ['task_name', 'status'])
TASK_DURATION = Histogram('celery_task_duration_seconds', 'Task duration', ['task_name'])
QUEUE_LENGTH = Gauge('celery_queue_length', 'Queue length', ['queue_name'])
WORKER_COUNT = Gauge('celery_workers_active', 'Active workers')

class MetricsTask(Task):
    """带监控指标的任务基类"""

    def __call__(self, *args, **kwargs):
        start_time = time.time()
        task_name = self.name

        try:
            result = super().__call__(*args, **kwargs)
            TASK_COUNTER.labels(task_name=task_name, status='success').inc()
            return result
        except Exception as exc:
            TASK_COUNTER.labels(task_name=task_name, status='failure').inc()
            raise
        finally:
            duration = time.time() - start_time
            TASK_DURATION.labels(task_name=task_name).observe(duration)

# 队列长度监控
@celery_app.task
def monitor_queue_lengths():
    """监控队列长度"""
    inspect = celery_app.control.inspect()
    active_queues = inspect.active_queues()

    if active_queues:
        for worker, queues in active_queues.items():
            for queue_info in queues:
                queue_name = queue_info['name']
                # 这里需要根据实际broker实现获取队列长度
                length = get_queue_length(queue_name)
                QUEUE_LENGTH.labels(queue_name=queue_name).set(length)
```

#### 2. 告警规则
```python
# app/core/monitoring/alerts.py
from app.core.celery_app import celery_app
import logging

logger = logging.getLogger(__name__)

@celery_app.task
def check_system_health():
    """系统健康检查"""
    alerts = []

    # 检查worker状态
    inspect = celery_app.control.inspect()
    stats = inspect.stats()

    if not stats:
        alerts.append({
            'level': 'critical',
            'message': 'No active Celery workers found'
        })

    # 检查队列积压
    for queue_name in ['download', 'processing', 'cleanup']:
        length = get_queue_length(queue_name)
        if length > 1000:
            alerts.append({
                'level': 'warning',
                'message': f'Queue {queue_name} has {length} pending tasks'
            })

    # 检查失败率
    failure_rate = get_recent_failure_rate()
    if failure_rate > 0.1:  # 10%
        alerts.append({
            'level': 'warning',
            'message': f'Task failure rate is {failure_rate:.2%}'
        })

    # 发送告警
    for alert in alerts:
        send_alert(alert)

    return {'alerts_count': len(alerts), 'alerts': alerts}

def send_alert(alert: dict):
    """发送告警"""
    if alert['level'] == 'critical':
        # 发送紧急通知
        send_critical_notification(alert['message'])
    else:
        # 记录日志
        logger.warning(f"Alert: {alert['message']}")
```

## 🧪 测试策略

### 单元测试

```python
# tests/test_celery_tasks.py
import pytest
from unittest.mock import patch, MagicMock
from app.core.celery.download_tasks import download_file_task


@pytest.mark.asyncio
async def test_download_file_task_success():
   """测试文件下载成功"""
   with patch('app.core.file.manager.file_manager.download_from_url') as mock_download:
      mock_file = MagicMock()
      mock_file.id = 123
      mock_file.file_hash = 'abc123'
      mock_file.local_path = '/path/to/file'
      mock_download.return_value = mock_file

      result = download_file_task('http://example.com/file.jpg')

      assert result['status'] == 'success'
      assert result['file_id'] == 123
      assert result['file_hash'] == 'abc123'


@pytest.mark.asyncio
async def test_download_file_task_retry():
   """测试文件下载重试"""
   with patch('app.core.file.manager.file_manager.download_from_url') as mock_download:
      mock_download.side_effect = httpx.TimeoutException("Timeout")

      with pytest.raises(httpx.TimeoutException):
         download_file_task('http://example.com/file.jpg')
```

### 集成测试

```python
# tests/test_celery_integration.py
import pytest
from celery import group
from app.core.celery import download_file_task, process_task_item


@pytest.mark.integration
def test_download_and_process_workflow():
   """测试下载和处理工作流"""
   # 创建下载任务组
   download_jobs = group([
      download_file_task.s('http://example.com/file1.jpg'),
      download_file_task.s('http://example.com/file2.jpg')
   ])

   # 执行下载
   download_result = download_jobs.apply()
   download_results = download_result.get()

   # 验证下载结果
   assert len(download_results) == 2
   assert all(r['status'] == 'success' for r in download_results)

   # 创建处理任务
   processing_jobs = []
   for result in download_results:
      job = process_task_item.s(
         file_id=result['file_id'],
         plugin_code='test_plugin',
         plugin_version='1.0.0'
      )
      processing_jobs.append(job)

   # 执行处理
   processing_group = group(processing_jobs)
   processing_result = processing_group.apply()
   processing_results = processing_result.get()

   # 验证处理结果
   assert len(processing_results) == 2
   assert all(r['status'] == 'completed' for r in processing_results)
```

### 压力测试

```python
# tests/test_celery_performance.py
import pytest
import time
from celery import group
from app.core.celery import download_file_task


@pytest.mark.performance
def test_concurrent_downloads():
   """测试并发下载性能"""
   urls = [f'http://example.com/file{i}.jpg' for i in range(100)]

   start_time = time.time()

   # 创建并发下载任务
   download_jobs = group([download_file_task.s(url) for url in urls])
   result = download_jobs.apply()
   results = result.get()

   end_time = time.time()
   duration = end_time - start_time

   # 验证性能指标
   assert len(results) == 100
   assert duration < 60  # 应该在60秒内完成
   assert sum(1 for r in results if r['status'] == 'success') >= 95  # 95%成功率
```

---

**文档版本**: v1.0
**最后更新**: 2025-01-01
**维护人员**: AI平台开发团队
