"""
系统管理API
提供系统监控、状态查询等功能
"""
from fastapi import APIRouter, Depends, HTTPException
from typing import Dict, Any
# 注意：原有的scheduler导入已删除，相关功能已迁移到Celery

router = APIRouter()


@router.get("/engine-pool/stats", summary="获取引擎池统计信息")
async def get_engine_pool_stats():
    """
    获取引擎池统计信息
    
    Returns:
        引擎池统计数据，包含缓存命中率、活跃引擎数等信息
    """
    try:
        from app.plugins.engines import get_engine_pool
        engine_pool = await get_engine_pool()
        return engine_pool.get_stats()
    except ImportError:
        raise HTTPException(status_code=404, detail="引擎池模块未找到")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取引擎池统计信息失败: {str(e)}")


@router.get("/health", summary="基础健康检查")
async def health_check():
    """基础健康检查"""
    return {"status": "ok", "message": "服务正常运行"}


# 注意：原有的调度器健康检查接口已删除，请使用 /api/v1/monitoring/celery/health


@router.get("/health/detailed", summary="详细健康检查")
async def detailed_health_check():
    """
    详细健康检查

    Returns:
        系统整体健康状态，包括数据库、Redis等
        注意：Celery相关健康检查请使用 /api/v1/monitoring/celery/health
    """
    try:
        from datetime import datetime

        # 基础系统健康检查
        return {
            "status": "ok",
            "message": "系统运行正常",
            "timestamp": datetime.now().isoformat(),
            "celery_health_endpoint": "/api/v1/monitoring/celery/health"
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }