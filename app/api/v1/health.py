"""
Celery健康检查和监控接口
"""
from typing import Dict, Any
from fastapi import APIRouter, HTTPException
from datetime import datetime

from app.core.celery_app import celery_app
from app.core.celery.worker_check import get_comprehensive_status, submit_test_task
from app.log import logger

router = APIRouter()

@router.get("/engine-pool/stats", summary="获取引擎池统计信息")
async def get_engine_pool_stats():
    """
    获取引擎池统计信息

    Returns:
        引擎池统计数据，包含缓存命中率、活跃引擎数等信息
    """
    try:
        from app.plugins.engines import get_engine_pool
        engine_pool = await get_engine_pool()
        return engine_pool.get_stats()
    except ImportError:
        raise HTTPException(status_code=404, detail="引擎池模块未找到")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取引擎池统计信息失败: {str(e)}")


@router.get("/health", summary="基础健康检查")
async def health_check():
    """基础健康检查"""
    return {"status": "ok", "message": "服务正常运行"}

@router.get("/celery/health")
async def celery_health() -> Dict[str, Any]:
    """
    Celery健康检查
    
    Returns:
        dict: 健康检查结果
    """
    try:
        # 检查worker状态
        inspect = celery_app.control.inspect()
        
        # 获取worker统计信息
        stats = inspect.stats()
        active = inspect.active()
        reserved = inspect.reserved()
        
        worker_count = len(stats) if stats else 0
        active_tasks = sum(len(tasks) for tasks in active.values()) if active else 0
        reserved_tasks = sum(len(tasks) for tasks in reserved.values()) if reserved else 0
        
        # 检查队列状态
        active_queues = inspect.active_queues()
        queue_info = {}
        if active_queues:
            for worker, queues in active_queues.items():
                for queue in queues:
                    queue_name = queue['name']
                    if queue_name not in queue_info:
                        queue_info[queue_name] = []
                    queue_info[queue_name].append(worker)
        
        # 确定整体健康状态
        status = "healthy" if worker_count > 0 else "no_workers"
        
        return {
            "status": status,
            "timestamp": datetime.now().isoformat(),
            "workers": {
                "count": worker_count,
                "active_tasks": active_tasks,
                "reserved_tasks": reserved_tasks
            },
            "queues": queue_info,
            "broker": {
                "url": str(celery_app.conf.broker_url),
                "transport": celery_app.conf.broker_transport
            }
        }
        
    except Exception as e:
        logger.error(f"Celery health check failed: {e}", exc_info=True)
        return {
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }


@router.get("/celery/tasks/{task_id}")
async def get_celery_task_status(task_id: str) -> Dict[str, Any]:
    """
    获取Celery任务状态
    
    Args:
        task_id: Celery任务ID
        
    Returns:
        dict: 任务状态信息
    """
    try:
        result = celery_app.AsyncResult(task_id)
        
        return {
            "task_id": task_id,
            "status": result.status,
            "result": result.result,
            "traceback": result.traceback,
            "date_done": result.date_done.isoformat() if result.date_done else None,
            "successful": result.successful(),
            "failed": result.failed(),
            "ready": result.ready()
        }
        
    except Exception as e:
        logger.error(f"Failed to get task status for {task_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/celery/tasks/{task_id}/revoke")
async def revoke_celery_task(task_id: str, terminate: bool = False) -> Dict[str, Any]:
    """
    撤销Celery任务
    
    Args:
        task_id: Celery任务ID
        terminate: 是否强制终止正在运行的任务
        
    Returns:
        dict: 撤销结果
    """
    try:
        celery_app.control.revoke(task_id, terminate=terminate)
        
        return {
            "task_id": task_id,
            "revoked": True,
            "terminated": terminate,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to revoke task {task_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/celery/workers")
async def get_celery_workers() -> Dict[str, Any]:
    """
    获取Celery Worker信息
    
    Returns:
        dict: Worker信息
    """
    try:
        inspect = celery_app.control.inspect()
        
        # 获取详细的worker信息
        stats = inspect.stats()
        active = inspect.active()
        reserved = inspect.reserved()
        registered = inspect.registered()
        
        workers_info = {}
        
        if stats:
            for worker_name, worker_stats in stats.items():
                workers_info[worker_name] = {
                    "stats": worker_stats,
                    "active_tasks": active.get(worker_name, []) if active else [],
                    "reserved_tasks": reserved.get(worker_name, []) if reserved else [],
                    "registered_tasks": registered.get(worker_name, []) if registered else []
                }
        
        return {
            "workers": workers_info,
            "total_workers": len(workers_info),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to get worker info: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/celery/queues")
async def get_celery_queues() -> Dict[str, Any]:
    """
    获取Celery队列信息
    
    Returns:
        dict: 队列信息
    """
    try:
        inspect = celery_app.control.inspect()
        
        # 获取活跃队列
        active_queues = inspect.active_queues()
        
        queue_summary = {}
        if active_queues:
            for worker, queues in active_queues.items():
                for queue in queues:
                    queue_name = queue['name']
                    if queue_name not in queue_summary:
                        queue_summary[queue_name] = {
                            "workers": [],
                            "routing_key": queue.get('routing_key', ''),
                            "exchange": queue.get('exchange', {})
                        }
                    queue_summary[queue_name]["workers"].append(worker)
        
        return {
            "queues": queue_summary,
            "total_queues": len(queue_summary),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to get queue info: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/celery/comprehensive")
async def comprehensive_celery_health() -> Dict[str, Any]:
    """
    综合Celery健康检查

    Returns:
        dict: 综合健康检查结果，包含建议
    """
    try:
        status = get_comprehensive_status()
        return status

    except Exception as e:
        logger.error(f"Comprehensive health check failed: {e}", exc_info=True)
        return {
            "overall_status": "error",
            "timestamp": datetime.now().isoformat(),
            "error": str(e),
            "recommendations": ["检查Celery配置和连接"]
        }


@router.post("/celery/test")
async def test_celery_task() -> Dict[str, Any]:
    """
    测试Celery任务提交

    Returns:
        dict: 测试结果
    """
    try:
        result = submit_test_task()
        return result

    except Exception as e:
        logger.error(f"Failed to submit test task: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))
