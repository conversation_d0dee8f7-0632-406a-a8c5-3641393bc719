"""
API V1版本路由
"""
from fastapi import APIRouter

from app.api.v1 import files, health, plugins, task, system

api_router = APIRouter()

# 注册路由
api_router.include_router(files.router, prefix="/files", tags=["文件管理"])
api_router.include_router(plugins.router, prefix="/plugins", tags=["插件管理"])
api_router.include_router(task.router, prefix="/tasks", tags=["任务管理"])
api_router.include_router(health.router, prefix="/monitor", tags=["监控"])