"""
应用配置管理模块
"""
import os
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timedelta, timezone

from pydantic import AnyHttpUrl, PostgresDsn, RedisDsn, validator, Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""
    # 应用基本信息
    APP_NAME: str = "AI推理平台"
    APP_VERSION: str = "1.0.0"
    APP_PORT: int = 8000
    DEBUG: bool = False

    # API
    API_V1_STR: str = "/api/v1"

    LOG_FILE_NAME: str = "app.log"
    # 每日午夜轮转
    LOG_FILE_ROTATION: str = "00:00"
    # 例如 "30 days", "10 files"
    LOG_FILE_RETENTION_POLICY: str = "7 days"

    # 数据库
    DATABASE_URL: PostgresDsn
    DB_ECHO: bool = False

    # Redis
    REDIS_URL: RedisDsn = "redis://localhost:6379/0"

    # 存储
    STORAGE_PATH: str = "/base/path"
    HTTP_CLIENT_TIMEOUT: int = 60  # HTTP客户端超时时间（秒）
    FILE_STORAGE_PATH: str = "storage"
    FILE_BASE_URL: str = "http://localhost:8000/files"
    PLUGIN_STORAGE_PATH: str = "plugins"
    
    # 时区配置
    TIMEZONE: timezone = timezone(timedelta(hours=8))  # 东八区
    
    # 任务调度配置 (已迁移到Celery)
    TASK_SCHEDULER_MAX_CONCURRENT_TASKS: int = 5  # 单个worker最大并发任务数
    TASK_SCHEDULER_GLOBAL_MAX_CONCURRENT_TASKS: int = 30  # 全局最大并发任务数
    TASK_SCHEDULER_HEARTBEAT_INTERVAL: int = 10  # 心跳间隔（秒）
    TASK_SCHEDULER_TASK_TIMEOUT: int = 300  # 任务超时时间（秒）

    # Celery配置
    CELERY_BROKER_URL: Optional[str] = None  # 将使用REDIS_URL
    CELERY_RESULT_BACKEND: Optional[str] = None  # 将使用REDIS_URL

    # Celery Worker配置
    CELERY_WORKER_CONCURRENCY: int = 4  # Worker并发数
    CELERY_WORKER_MAX_TASKS_PER_CHILD: int = 1000  # 每个子进程最大任务数
    CELERY_WORKER_PREFETCH_MULTIPLIER: int = 1  # 预取倍数

    # Celery任务配置
    CELERY_TASK_TIME_LIMIT: int = 300  # 任务硬超时（秒）
    CELERY_TASK_SOFT_TIME_LIMIT: int = 240  # 任务软超时（秒）
    CELERY_TASK_MAX_RETRIES: int = 5  # 最大重试次数
    CELERY_TASK_RETRY_BACKOFF_MAX: int = 300  # 最大退避时间（秒）

    # Celery监控配置
    CELERY_FLOWER_PORT: int = 5555  # Flower监控端口

    # 保留的任务调度配置（向后兼容）
    TASK_SCHEDULER_LOCK_TIMEOUT: int = 60  # 锁超时时间（秒）
    TASK_SCHEDULER_RETRY_MAX_ATTEMPTS: int = 3  # 最大重试次数
    TASK_SCHEDULER_RETRY_DELAY_BASE: int = 5  # 重试延迟基数（秒）
    TASK_SCHEDULER_BATCH_SIZE: int = 10  # 任务项批量处理大小
    TASK_SCHEDULER_POLL_INTERVAL: int = 1  # 轮询间隔（秒）
    TASK_SCHEDULER_STALE_TASK_TIMEOUT: int = 300  # 卡住任务检测超时（秒）

    @validator('CELERY_BROKER_URL', pre=True, always=True)
    def set_celery_broker_url(cls, v, values):
        """设置Celery Broker URL，默认使用Redis URL"""
        if v is None:
            redis_url = values.get('REDIS_URL')
            if redis_url:
                # 确保RedisDsn对象正确转换为字符串
                if hasattr(redis_url, '__str__'):
                    return str(redis_url)
                elif hasattr(redis_url, 'unicode_string'):
                    return redis_url.unicode_string()
                else:
                    return redis_url
            return None
        return str(v) if v else None

    @validator('CELERY_RESULT_BACKEND', pre=True, always=True)
    def set_celery_result_backend(cls, v, values):
        """设置Celery Result Backend，默认使用Redis URL"""
        if v is None:
            redis_url = values.get('REDIS_URL')
            if redis_url:
                # 确保RedisDsn对象正确转换为字符串
                if hasattr(redis_url, '__str__'):
                    return str(redis_url)
                elif hasattr(redis_url, 'unicode_string'):
                    return redis_url.unicode_string()
                else:
                    return redis_url
            return None
        return str(v) if v else None

    # 阿里云 OSS 配置
    OSS_ACCESS_KEY_ID: str
    OSS_ACCESS_KEY_SECRET: str
    OSS_ENDPOINT: str
    OSS_BUCKET_NAME: str
    OSS_ROLE_ARN: str  # STS 角色 ARN
    OSS_REGION: str = "cn-shanghai"  # OSS 区域

    # OpenObserve 日志采集配置
    OPENOBSERVE_URL: str = "http://192.168.10.202:5080"
    OPENOBSERVE_ORG: str = "default"
    OPENOBSERVE_USERNAME: str = "<EMAIL>"
    OPENOBSERVE_PASSWORD: str = ""
    OPENOBSERVE_STREAM: str = "fastapi-logs"

    # OpenTelemetry 分布式追踪配置
    OPENTELEMETRY_TRACE_ENABLED: bool = True  # 是否启用分布式追踪
    OPENTELEMETRY_TRACE_SAMPLE_RATE: float = 1.0  # 采样率，1.0表示100%采样
    OPENTELEMETRY_INSTRUMENT_SQLALCHEMY: bool = True  # 是否追踪SQLAlchemy数据库操作
    OPENTELEMETRY_INSTRUMENT_REDIS: bool = True  # 是否追踪Redis操作

    @staticmethod
    def DATETIME_DELTA(**kwargs) -> timedelta:
        """计算时间增量的快捷方法"""
        return timedelta(**kwargs)

    class Config:
        """配置元数据"""
        env_file = ".env"
        case_sensitive = True


# 创建全局配置对象
settings = Settings() 