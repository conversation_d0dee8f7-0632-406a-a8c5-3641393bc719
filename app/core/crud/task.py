"""
任务CRUD操作
"""
from typing import Dict, List, Optional, Any, Union
from datetime import datetime

from sqlalchemy import select, and_, or_, func
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.crud.base import CRUDBase
from app.models.task import Task
from app.schemas.task import TaskCreate, TaskUpdate, TaskFilter


class CRUDTask(CRUDBase[Task, TaskCreate, TaskUpdate]):
    """任务CRUD操作类"""
    
    async def get_by_status(
        self,
        db: AsyncSession,
        *,
        status: Union[str, List[str]]
    ) -> List[Task]:
        """
        根据状态获取任务列表
        
        Args:
            db: 数据库会话
            status: 状态或状态列表
            
        Returns:
            List[Task]: 任务列表
        """
        if isinstance(status, str):
            status = [status]
            
        result = await db.execute(
            select(self.model)
            .where(self.model.status.in_(status))
            .order_by(self.model.priority.desc(), self.model.created_at.asc())
        )
        return result.scalars().all()
    
    async def get_next_pending_task(
        self,
        db: AsyncSession
    ) -> Optional[Task]:
        """
        获取下一个待处理的任务
        按优先级降序、创建时间升序排序
        
        Args:
            db: 数据库会话
            
        Returns:
            Optional[Task]: 任务对象或None
        """
        result = await db.execute(
            select(self.model)
            .where(self.model.status == "pending")
            .order_by(self.model.priority.desc(), self.model.created_at.asc())
            .limit(1)
        )
        return result.scalars().first()

    
    async def get_filtered(
        self,
        db: AsyncSession,
        *,
        filter_params: TaskFilter,
        skip: int = 0,
        limit: int = 10
    ) -> List[Task]:
        """
        根据过滤条件获取任务列表
        
        Args:
            db: 数据库会话
            filter_params: 过滤参数
            skip: 跳过记录数
            limit: 返回记录数上限
            
        Returns:
            List[Task]: 任务列表
        """
        query = select(self.model)
        
        # 构建过滤条件
        conditions = []
        
        if filter_params.status:
            conditions.append(self.model.status == filter_params.status)
            
        if filter_params.priority:
            conditions.append(self.model.priority == filter_params.priority)
            
        if filter_params.start_time:
            conditions.append(self.model.created_at >= filter_params.start_time)
            
        if filter_params.end_time:
            conditions.append(self.model.created_at <= filter_params.end_time)
        
        if conditions:
            query = query.where(and_(*conditions))
            
        # 排序（添加稳定排序：created_at desc, id desc）
        query = query.order_by(self.model.created_at.desc(), self.model.id.desc())

        # 分页
        query = query.offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()
    
    async def count_filtered(
        self,
        db: AsyncSession,
        *,
        filter_params: TaskFilter
    ) -> int:
        """
        根据过滤条件统计任务数量（优化：使用数据库COUNT函数）

        Args:
            db: 数据库会话
            filter_params: 过滤参数

        Returns:
            int: 任务数量
        """
        # 使用 func.count() 进行高效计数
        query = select(func.count()).select_from(self.model)

        # 构建过滤条件
        conditions = []

        if filter_params.status:
            conditions.append(self.model.status == filter_params.status)

        if filter_params.priority:
            conditions.append(self.model.priority == filter_params.priority)

        if filter_params.start_time:
            conditions.append(self.model.created_at >= filter_params.start_time)

        if filter_params.end_time:
            conditions.append(self.model.created_at <= filter_params.end_time)

        if conditions:
            query = query.where(and_(*conditions))

        result = await db.execute(query)
        return result.scalar() or 0

    async def get_stale_tasks(
        self,
        db: AsyncSession,
        cutoff_time: Any
    ) -> List[Task]:
        """
        获取卡住的任务（长时间处于initializing或running状态）

        Args:
            db: 数据库会话
            cutoff_time: 截止时间

        Returns:
            List[Task]: 卡住的任务列表
        """
        result = await db.execute(
            select(self.model)
            .where(
                and_(
                    self.model.status.in_(['initializing', 'running']),
                    self.model.updated_at < cutoff_time
                )
            )
            .order_by(self.model.updated_at.asc())
        )
        return result.scalars().all()

    async def get_failed_tasks_before_date(
        self,
        db: AsyncSession,
        cutoff_date: Any
    ) -> List[Task]:
        """
        获取指定日期之前的失败任务

        Args:
            db: 数据库会话
            cutoff_date: 截止日期

        Returns:
            List[Task]: 失败任务列表
        """
        result = await db.execute(
            select(self.model)
            .where(
                and_(
                    self.model.status == 'failed',
                    self.model.completed_at < cutoff_date
                )
            )
            .order_by(self.model.completed_at.asc())
        )
        return result.scalars().all()

    async def get_status_statistics(
        self,
        db: AsyncSession
    ) -> Dict[str, int]:
        """
        获取任务状态统计

        Args:
            db: 数据库会话

        Returns:
            Dict[str, int]: 状态统计
        """
        result = await db.execute(
            select(
                self.model.status,
                func.count(self.model.id).label('count')
            )
            .group_by(self.model.status)
        )

        stats = {}
        for row in result:
            stats[row.status] = row.count

        return stats

    async def count(self, db: AsyncSession) -> int:
        """
        获取任务总数

        Args:
            db: 数据库会话

        Returns:
            int: 任务总数
        """
        result = await db.execute(
            select(func.count()).select_from(self.model)
        )
        return result.scalar() or 0


# 创建全局CRUD实例
task_crud = CRUDTask(Task)