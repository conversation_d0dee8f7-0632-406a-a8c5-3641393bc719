"""
任务管理器 - 已迁移到Celery
负责任务的创建、状态更新和查询
"""
from typing import Dict, List, Any, Optional
from datetime import datetime
import os

from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import HTTPException, status

from app.models.task import Task, TaskItem
from app.core.crud.task import task_crud
from app.core.crud.task_item import task_item_crud
from app.core.file.manager import file_manager
from app.schemas.file import File
from app.schemas.task import TaskFilter, TaskItemCreate, TaskCreate, TaskResponse, TaskItemResponse
from app.log import task_logger as logger
from app.utils.file import process_oss_url, to_absolute_path
from app.utils.trace_context_utils import traced_async_task, add_span_attributes, serialize_trace_context
from app.schemas.base import MediaType, PageResponse, PageParams

# 导入Celery任务管理器
from app.core.celery.celery_manager import celery_task_manager


class TaskManager:
    """任务管理器 - 使用Celery后端"""

    @staticmethod
    async def create_task(
            db: AsyncSession,
            *,
            task_in: TaskCreate
    ) -> TaskResponse:
        """
        创建新任务 - 使用Celery后端

        Args:
            db: 数据库会话
            task_in: 任务创建参数

        Returns:
            TaskResponse: 创建的任务响应

        Raises:
            HTTPException: 创建失败时抛出
        """
        try:
            # 插件验证
            if not task_in.plugins or len(task_in.plugins) == 0:
                raise ValueError("未提供插件信息")

            # 验证所有插件是否存在且已启用
            from app.core.crud.plugin import plugin_crud
            for plugin_config in task_in.plugins:
                plugin_code = plugin_config.plugin_code
                plugin_version = plugin_config.plugin_version

                plugin = await plugin_crud.get_by_code_version(
                    db,
                    plugin_code=plugin_code,
                    plugin_version=plugin_version
                )

                if not plugin:
                    raise ValueError(f"插件 {plugin_code} 版本 {plugin_version} 不存在")

                if plugin.status != "enabled":
                    raise ValueError(f"插件 {plugin_code} 版本 {plugin_version} 未启用，当前状态: {plugin.status}")

            # 添加数据库操作的span属性
            add_span_attributes(
                db_operation="task_create",
                total_items=len(task_in.items),
                total_plugins=len(task_in.plugins)
            )

            # 使用Celery任务管理器创建任务
            logger.info(f"Creating Celery task with {len(task_in.items)} items and {len(task_in.plugins)} plugins")
            return await celery_task_manager.create_task(db, task_in)

            # 传入任务对象获取进度
            return await task_manager.get_task_progress(db, task=task)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"创建任务失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"创建任务失败: {str(e)}"
            )

    # 注意：原有的 _process_task_items 方法已被Celery替代，相关逻辑已迁移到 app/core/celery/ 模块

    @staticmethod
    async def get_tasks(
            db: AsyncSession,
            *,
            filter_params: TaskFilter,
            page_params: PageParams
    ) -> PageResponse[TaskResponse]:
        """
        获取任务列表
        
        Args:
            db: 数据库会话
            filter_params: 过滤参数
            skip: 跳过记录数
            limit: 返回记录数上限
            
        Returns:
            PageResponse[TaskResponse]: 任务列表
        """
        # 获取总数（修复：使用正确的计数方法）
        total = await task_crud.count_filtered(db, filter_params=filter_params)

        # 计算skip和limit
        skip = (page_params.page - 1) * page_params.page_size
        limit = page_params.page_size

        # 获取任务列表
        tasks = await task_crud.get_filtered(
            db,
            filter_params=filter_params,
            skip=skip,
            limit=limit
        )

        # 为每个任务获取进度信息
        result = []
        for task in tasks:
            # 直接传入task对象
            task_info = await TaskManager.get_task_progress(db, task=task)
            result.append(task_info)

        return PageResponse[TaskResponse](
            page=page_params.page,
            page_size=page_params.page_size,
            total_count=total,
            total_page=(total + limit - 1) // limit,
            items=result
        )

    @staticmethod
    async def get_task(
            db: AsyncSession,
            task_id: int
    ) -> TaskResponse:
        """
        获取任务详情

        Args:
            db: 数据库会话
            task_id: 任务ID

        Returns:
            TaskResponse: 任务响应对象

        Raises:
            HTTPException: 任务不存在时抛出
        """
        task = await task_crud.get(db, id=task_id)
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )
        # 传入任务对象获取进度
        return await task_manager.get_task_progress(db, task=task)

    @staticmethod
    async def get_task_model(
            db: AsyncSession,
            task_id: int
    ) -> Optional[Task]:
        """
        获取任务模型对象（内部使用）

        Args:
            db: 数据库会话
            task_id: 任务ID

        Returns:
            Optional[Task]: 任务模型对象或None
        """
        return await task_crud.get(db, id=task_id)

    @staticmethod
    async def get_task_items(
            db: AsyncSession,
            *,
            task_id: int,
            page_params: PageParams
    ) -> PageResponse[TaskItemResponse]:
        """
        获取任务的所有任务项
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            skip: 跳过记录数
            limit: 返回记录数上限
            
        Returns:
            PageResponse[TaskItemResponse]: 任务项列表
        """
        # 获取总数
        total = await task_item_crud.count_by_task_id(db, task_id=task_id)

        # 计算skip和limit
        skip = (page_params.page - 1) * page_params.page_size
        limit = page_params.page_size

        items = await task_item_crud.get_by_task_id_paginated(
            db,
            task_id=task_id,
            skip=skip,
            limit=limit
        )

        # 转换为API响应格式
        result = []
        for item in items:
            result.append({
                "task_id": item.task_id,
                "task_item_id": item.id,
                "data_id": item.data_id,
                "file_type": item.file_type,
                "media_type": item.media_type,
                "file_hash": item.file_hash,
                "file_url": item.file_url,
                "status": item.status,
                "error": item.error,
                "plugin_code": item.plugin_code,
                "plugin_version": item.plugin_version,
                "result": item.result,
                "started_at": item.started_at,
                "completed_at": item.completed_at
            })

        return PageResponse[TaskItemResponse](
            page=page_params.page,
            page_size=page_params.page_size,
            total_count=total,
            total_page=(total + limit - 1) // limit,
            items=result
        )

    @staticmethod
    async def get_task_progress(
            db: AsyncSession,
            task: Task  # 直接接收Task对象而不是task_id
    ) -> TaskResponse:
        """
        获取任务实时进度详情

        Args:
            db: 数据库会话
            task: 任务对象

        Returns:
            Dict[str, Any]: 任务进度详情
        """
        # 获取已完成任务项数量（包括completed、failed和canceled状态）
        finished_items = await task_item_crud.count_by_task_id(db, task_id=task.id, status=["completed", "failed", "canceled"])

        # 构建进度信息
        return TaskResponse(
            task_id=task.id,
            progress=f"{finished_items}/{task.total_items}",
            status=task.status,
            priority=task.priority,
            created_at=task.created_at,
            updated_at=task.updated_at,
            started_at=task.started_at,
            completed_at=task.completed_at
        )

    @staticmethod
    async def update_task_status(
            db: AsyncSession,
            *,
            task_id: int,
            status: str,
            error: Optional[str] = None
    ) -> Task:
        """
        更新任务状态
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            status: 新状态
            error: 错误信息
            
        Returns:
            Task: 更新后的任务对象
        """
        task = await task_crud.get(db, id=task_id)
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )

        update_data = {
            "status": status,
            "updated_at": datetime.now()
        }

        # 更新时间戳
        if status == "running" and not task.started_at:
            update_data["started_at"] = datetime.now()
        elif status in ["completed", "failed", "canceled"]:
            update_data["completed_at"] = datetime.now()

        updated_task = await task_crud.update(db, db_obj=task, obj_in=update_data)
        return updated_task

    @staticmethod
    async def update_task_priority(
            db: AsyncSession,
            *,
            task_id: int,
            priority: int
    ) -> TaskResponse:
        """
        更新任务优先级
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            priority: 新的优先级
            
        Returns:
            Task: 更新后的任务对象
        """
        task = await task_crud.get(db, id=task_id)
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )

        if task.status in ["completed", "failed", "canceled"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"任务已{task.status}，无法调整优先级"
            )

        updated_task = await task_crud.update(
            db,
            db_obj=task,
            obj_in={
                "priority": priority,
                "updated_at": datetime.now()
            }
        )

        # 注意：原有的调度队列逻辑已被Celery替代

        # 传入任务对象获取进度
        return await task_manager.get_task_progress(db, task=updated_task)


    @staticmethod
    async def cancel_task(
            db: AsyncSession,
            task_id: int
    ) -> TaskResponse:
        """
        取消任务 - 使用Celery后端

        Args:
            db: 数据库会话
            task_id: 任务ID

        Returns:
            任务信息

        Raises:
            ValueError: 任务不存在或任务状态不允许取消
        """
        logger.info(f"Canceling Celery task {task_id}")

        # 使用Celery任务管理器取消任务
        success = await celery_task_manager.cancel_task(db, task_id)

        if not success:
            raise ValueError("任务取消失败")

        # 返回更新后的任务信息
        return await celery_task_manager.get_task_progress(db, task_id=task_id)

    @staticmethod
    async def get_pending_items(
            db: AsyncSession,
            task_id: int,
            limit: Optional[int] = None
    ) -> List[TaskItem]:
        """
        获取任务的待处理任务项
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            limit: 返回数量限制
            
        Returns:
            List[TaskItem]: 待处理的任务项列表
        """
        return await task_item_crud.get_by_status(
            db,
            task_id=task_id,
            status="pending",
            limit=limit
        )

    @staticmethod
    async def update_task_item(
            db: AsyncSession,
            *,
            id: int,
            update_data: Dict[str, Any]
    ) -> TaskItem:
        """
        更新任务项信息

        Args:
            db: 数据库会话
            id: 任务项ID
            update_data: 更新数据

        Returns:
            TaskItem: 更新后的任务项
        """
        item = await task_item_crud.get(db, id=id)
        if not item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"任务项 {id} 不存在"
            )
        return await task_item_crud.update(db, db_obj=item, obj_in=update_data)

    @staticmethod
    async def update_task_item_direct(
            db: AsyncSession,
            *,
            id: int,
            update_data: Dict[str, Any]
    ) -> bool:
        """
        直接更新任务项信息（不先查询，用于性能优化）

        Args:
            db: 数据库会话
            id: 任务项ID
            update_data: 更新数据

        Returns:
            bool: 是否更新成功
        """
        return await task_item_crud.update_direct(db, id=id, update_data=update_data)

    @staticmethod
    async def get_task_item(
            db: AsyncSession,
            *,
            id: int
    ) -> Optional[TaskItem]:
        """获取单个任务项"""
        return await task_item_crud.get(db, id=id)




# 创建全局任务管理器实例
task_manager = TaskManager()
