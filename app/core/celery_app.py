"""
Celery应用配置
"""
from celery import Celery
from app.config.settings import settings

# 创建Celery应用实例
celery_app = Celery(
    "ai-platform",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND,
    include=[
        'app.core.celery.download_tasks',
        'app.core.celery.processing_tasks',
        'app.core.celery.management_tasks'
    ]
)

# Celery配置
celery_app.conf.update(
    # 序列化配置
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='Asia/Shanghai',
    enable_utc=True,
    
    # 可靠性配置
    task_acks_late=True,
    task_reject_on_worker_lost=True,
    worker_prefetch_multiplier=1,
    
    # 重试配置
    result_backend_always_retry=True,
    result_backend_max_retries=10,
    result_backend_max_sleep_between_retries_ms=10000,
    result_backend_base_sleep_between_retries_ms=10,
    
    # 任务配置
    task_time_limit=300,  # 5分钟硬超时
    task_soft_time_limit=240,  # 4分钟软超时
    task_track_started=True,
    task_store_errors_even_if_ignored=True,
    
    # 队列路由配置
    task_routes={
        'download_file_task': {'queue': 'download'},
        'batch_download_files': {'queue': 'download'},
        'process_task_item': {'queue': 'processing'},
        'process_task_workflow': {'queue': 'processing'},
        'cleanup_failed_tasks': {'queue': 'management'},
        'update_task_status': {'queue': 'management'},
        'system_health_check': {'queue': 'management'},
        'test_download_task': {'queue': 'download'},
    },
    
    # 全局任务注解
    task_annotations={
        '*': {
            'rate_limit': '100/s',
        },
        'download_file_task': {
            'autoretry_for': (Exception,),
            'retry_kwargs': {'max_retries': 5},
            'retry_backoff': True,
            'retry_backoff_max': 300,
            'retry_jitter': True,
        },
        'process_task_item': {
            'autoretry_for': (Exception,),
            'retry_kwargs': {'max_retries': 3},
            'retry_backoff': True,
            'retry_backoff_max': 120,
            'retry_jitter': True,
        }
    },
    
    # Worker配置
    worker_max_tasks_per_child=1000,
    worker_disable_rate_limits=False,
    
    # 监控配置
    worker_send_task_events=True,
    task_send_sent_event=True,
)

# 任务发现和注册
celery_app.autodiscover_tasks([
    'app.core.celery.download_tasks',
    'app.core.celery.processing_tasks',
    'app.core.celery.management_tasks'
])

__all__ = ['celery_app']
