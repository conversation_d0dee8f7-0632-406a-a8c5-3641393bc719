"""
管理和维护相关的Celery任务
"""
import asyncio
from typing import Dict, Any, List
from datetime import datetime, timedelta
from celery import Task

from app.core.celery_app import celery_app
from app.db.session import get_async_db
from app.core.crud.task import task_crud
from app.core.crud.task_item import task_item_crud
from app.log import logger


class ManagementTask(Task):
    """管理任务基类"""
    
    def on_success(self, retval, task_id, args, kwargs):
        """任务成功回调"""
        logger.info(f"Management task {task_id} succeeded")
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """任务失败回调"""
        logger.error(f"Management task {task_id} failed: {exc}")


@celery_app.task(
    bind=True,
    base=ManagementTask,
    name='cleanup_failed_tasks'
)
def cleanup_failed_tasks(self, days_old: int = 7) -> Dict[str, Any]:
    """
    清理失败的任务
    
    Args:
        days_old: 清理多少天前的失败任务
        
    Returns:
        dict: 清理结果
    """
    try:
        logger.info(f"Starting cleanup of failed tasks older than {days_old} days")
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            async def cleanup():
                cutoff_date = datetime.now() - timedelta(days=days_old)
                
                async with get_async_db() as db:
                    # 查找需要清理的失败任务
                    failed_tasks = await task_crud.get_failed_tasks_before_date(db, cutoff_date)
                    
                    cleaned_count = 0
                    for task in failed_tasks:
                        # 删除相关的任务项
                        await task_item_crud.delete_by_task_id(db, task_id=task.id)
                        
                        # 删除任务记录
                        await task_crud.delete(db, id=task.id)
                        
                        cleaned_count += 1
                    
                    return cleaned_count
            
            cleaned_count = loop.run_until_complete(cleanup())
            
            result = {
                'status': 'success',
                'cleaned_tasks': cleaned_count,
                'cutoff_date': (datetime.now() - timedelta(days=days_old)).isoformat()
            }
            
            logger.info(f"Cleanup completed: {cleaned_count} tasks cleaned")
            return result
            
        finally:
            loop.close()
            
    except Exception as exc:
        logger.error(f"Cleanup failed: {exc}", exc_info=True)
        return {
            'status': 'failed',
            'error': str(exc)
        }


@celery_app.task(
    bind=True,
    base=ManagementTask,
    name='update_task_status'
)
def update_task_status(self, task_id: int, status: str, error: str = None) -> Dict[str, Any]:
    """
    更新任务状态
    
    Args:
        task_id: 任务ID
        status: 新状态
        error: 错误信息（可选）
        
    Returns:
        dict: 更新结果
    """
    try:
        logger.info(f"Updating task {task_id} status to {status}")
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            async def update():
                async with get_async_db() as db:
                    update_data = {
                        'status': status,
                        'updated_at': datetime.now()
                    }
                    
                    if error:
                        update_data['error'] = error
                    
                    if status in ['completed', 'failed', 'canceled']:
                        update_data['completed_at'] = datetime.now()
                    
                    # 获取任务对象
                    task = await task_crud.get(db, id=task_id)
                    if task:
                        await task_crud.update(db, db_obj=task, obj_in=update_data)
                        return True
                    return False
            
            success = loop.run_until_complete(update())
            
            return {
                'status': 'success',
                'task_id': task_id,
                'new_status': status,
                'updated': success
            }
            
        finally:
            loop.close()
            
    except Exception as exc:
        logger.error(f"Status update failed for task {task_id}: {exc}", exc_info=True)
        return {
            'status': 'failed',
            'task_id': task_id,
            'error': str(exc)
        }


@celery_app.task(
    bind=True,
    base=ManagementTask,
    name='check_stale_tasks'
)
def check_stale_tasks(self, timeout_minutes: int = 30) -> Dict[str, Any]:
    """
    检查并处理卡住的任务
    
    Args:
        timeout_minutes: 超时分钟数
        
    Returns:
        dict: 检查结果
    """
    try:
        logger.info(f"Checking for stale tasks (timeout: {timeout_minutes} minutes)")
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            async def check_and_fix():
                cutoff_time = datetime.now() - timedelta(minutes=timeout_minutes)
                
                async with get_async_db() as db:
                    # 查找卡在initializing或running状态的任务
                    stale_tasks = await task_crud.get_stale_tasks(db, cutoff_time)
                    
                    fixed_count = 0
                    for task in stale_tasks:
                        # 将卡住的任务标记为失败
                        await task_crud.update(db, db_obj=task, obj_in={
                            'status': 'failed',
                            'error': f'Task timed out after {timeout_minutes} minutes',
                            'completed_at': datetime.now()
                        })

                        fixed_count += 1
                    
                    return fixed_count
            
            fixed_count = loop.run_until_complete(check_and_fix())
            
            result = {
                'status': 'success',
                'fixed_tasks': fixed_count,
                'timeout_minutes': timeout_minutes
            }
            
            logger.info(f"Stale task check completed: {fixed_count} tasks fixed")
            return result
            
        finally:
            loop.close()
            
    except Exception as exc:
        logger.error(f"Stale task check failed: {exc}", exc_info=True)
        return {
            'status': 'failed',
            'error': str(exc)
        }


@celery_app.task(
    bind=True,
    base=ManagementTask,
    name='system_health_check'
)
def system_health_check(self) -> Dict[str, Any]:
    """
    系统健康检查
    
    Returns:
        dict: 健康检查结果
    """
    try:
        logger.info("Starting system health check")
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            async def health_check():
                async with get_async_db() as db:
                    # 检查数据库连接
                    db_healthy = True
                    try:
                        await task_crud.count(db)
                    except Exception:
                        db_healthy = False
                    
                    # 统计任务状态
                    task_stats = await task_crud.get_status_statistics(db)
                    
                    return {
                        'database': 'healthy' if db_healthy else 'unhealthy',
                        'task_statistics': task_stats
                    }
            
            health_data = loop.run_until_complete(health_check())
            
            # 检查Celery worker状态
            from app.core.celery_app import celery_app
            inspect = celery_app.control.inspect()
            
            worker_stats = {}
            try:
                stats = inspect.stats()
                active = inspect.active()
                
                worker_stats = {
                    'workers_count': len(stats) if stats else 0,
                    'active_tasks': sum(len(tasks) for tasks in active.values()) if active else 0,
                    'workers_healthy': bool(stats)
                }
            except Exception as e:
                worker_stats = {
                    'workers_count': 0,
                    'active_tasks': 0,
                    'workers_healthy': False,
                    'error': str(e)
                }
            
            result = {
                'status': 'success',
                'timestamp': datetime.now().isoformat(),
                'database': health_data['database'],
                'celery_workers': worker_stats,
                'task_statistics': health_data['task_statistics']
            }
            
            logger.info("System health check completed")
            return result
            
        finally:
            loop.close()
            
    except Exception as exc:
        logger.error(f"Health check failed: {exc}", exc_info=True)
        return {
            'status': 'failed',
            'error': str(exc),
            'timestamp': datetime.now().isoformat()
        }


# 定期任务配置（需要celery beat）
@celery_app.task(name='periodic_cleanup')
def periodic_cleanup():
    """定期清理任务"""
    # 清理7天前的失败任务
    cleanup_result = cleanup_failed_tasks.delay(days_old=7)
    
    # 检查卡住的任务
    stale_check_result = check_stale_tasks.delay(timeout_minutes=30)
    
    return {
        'cleanup_task_id': cleanup_result.id,
        'stale_check_task_id': stale_check_result.id
    }
