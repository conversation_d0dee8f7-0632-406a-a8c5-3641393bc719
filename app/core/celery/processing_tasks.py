"""
任务处理相关的Celery任务
"""
import asyncio
import copy
from typing import Dict, Any, List
from celery import Task, group, chord
from datetime import datetime

from app.core.celery_app import celery_app
from app.db.session import get_async_db
from app.core.crud.task import task_crud
from app.core.crud.task_item import task_item_crud
from app.core.plugin.manager import PluginManager
from app.log import logger


class ProcessingTask(Task):
    """任务处理基类"""
    
    def on_success(self, retval, task_id, args, kwargs):
        """任务成功回调"""
        logger.info(f"Processing task {task_id} succeeded")
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """任务失败回调"""
        logger.error(f"Processing task {task_id} failed: {exc}")
    
    def on_retry(self, exc, task_id, args, kwargs, einfo):
        """任务重试回调"""
        logger.warning(f"Processing task {task_id} retrying: {exc}")


@celery_app.task(
    bind=True,
    base=ProcessingTask,
    name='process_task_item',
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 3},
    retry_backoff=True,
    retry_backoff_max=120,
    retry_jitter=True
)
def process_task_item(
    self, 
    task_item_id: int, 
    file_id: int, 
    plugin_code: str, 
    plugin_version: str, 
    params: dict = None
) -> Dict[str, Any]:
    """
    处理单个任务项
    
    Args:
        task_item_id: 任务项ID
        file_id: 文件ID
        plugin_code: 插件代码
        plugin_version: 插件版本
        params: 处理参数
        
    Returns:
        dict: 处理结果
    """
    try:
        logger.info(f"Processing task item {task_item_id} with plugin {plugin_code}:{plugin_version}")
        
        # 创建异步事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # 获取文件信息和插件处理
            async def process_item():
                async with get_async_db() as db:
                    # 获取文件信息
                    from app.core.crud.file import file_crud
                    file_info = await file_crud.get(db, id=file_id)
                    if not file_info:
                        raise ValueError(f"File {file_id} not found")

                    # 获取插件信息
                    from app.core.crud.plugin import plugin_crud
                    plugin = await plugin_crud.get_by_code_version(
                        db,
                        plugin_code=plugin_code,
                        plugin_version=plugin_version
                    )

                    if not plugin:
                        raise ValueError(f"Plugin {plugin_code}:{plugin_version} not found")

                    if plugin.status != "enabled":
                        raise ValueError(f"Plugin {plugin_code}:{plugin_version} is not enabled")

                    # 检查缓存
                    from app.core.crud.result_cache import result_cache_crud
                    from app.utils.hash import calculate_params_hash

                    params_hash = calculate_params_hash(params or {})
                    cache = await result_cache_crud.get_by_composite_key(
                        db,
                        file_hash=file_info.file_hash,
                        plugin_code=plugin_code,
                        plugin_version=plugin_version,
                        params_hash=params_hash
                    )

                    if cache:
                        # 缓存命中，直接返回缓存结果
                        logger.info(f"Cache hit for file {file_info.file_hash} with plugin {plugin_code}:{plugin_version}")
                        return {
                            "plugin_code": plugin_code,
                            "plugin_version": plugin_version,
                            "file_id": file_id,
                            "file_path": file_info.local_path,
                            "status": "completed",
                            "from_cache": True,
                            "result": cache.result,
                            "process_time": cache.process_time
                        }

                    # 缓存未命中，执行插件处理
                    from app.plugins.executor import PluginExecutor
                    from app.utils.file import to_absolute_path
                    import time

                    # 准备插件信息（参考原有的格式）
                    plugin_info = {
                        "plugin_code": plugin.plugin_code,
                        "plugin_version": plugin.plugin_version,
                        "engine": plugin.engine,
                        "python_file_path": to_absolute_path(plugin.python_file_path),
                        "config_file_path": to_absolute_path(plugin.config_file_path),
                        "model_file_path": to_absolute_path(plugin.model_file_path) if plugin.model_file_path else None
                    }

                    # 准备输入数据（参考原有的格式）
                    input_data = {
                        "file_path": to_absolute_path(file_info.local_path),
                        "file_type": file_info.file_type,
                        "media_type": file_info.media_type,
                        "file_hash": file_info.file_hash,
                        "task_item_id": task_item_id,
                        "params": params or {}
                    }

                    # 在插件执行前进行深拷贝，确保参数不会被插件修改
                    input_data_copy = copy.deepcopy(input_data)

                    # 执行插件（使用原有的插件执行器）
                    start_time = time.time()
                    try:
                        plugin_result = await PluginExecutor.execute_plugin(plugin_info, input_data_copy)
                        process_time = time.time() - start_time

                        # 保存到缓存
                        cache_data = {
                            "file_hash": file_info.file_hash,
                            "plugin_code": plugin_code,
                            "plugin_version": plugin_version,
                            "params_hash": params_hash,
                            "result": plugin_result,
                            "process_time": process_time
                        }

                        await result_cache_crud.create(db, obj_in=cache_data)

                        return {
                            "plugin_code": plugin_code,
                            "plugin_version": plugin_version,
                            "file_id": file_id,
                            "file_path": file_info.local_path,
                            "status": "completed",
                            "from_cache": False,
                            "result": plugin_result,
                            "process_time": process_time
                        }

                    except Exception as plugin_error:
                        process_time = time.time() - start_time
                        logger.error(f"Plugin execution failed: {plugin_error}", exc_info=True)
                        raise ValueError(f"Plugin execution failed: {str(plugin_error)}")

            result = loop.run_until_complete(process_item())
            
            # 更新任务项状态为完成
            async def update_task_item():
                async with get_async_db() as db:
                    # 获取任务项对象
                    task_item = await task_item_crud.get(db, id=task_item_id)
                    if task_item:
                        await task_item_crud.update(db, db_obj=task_item, obj_in={
                            'status': 'completed',
                            'result': result,
                            'completed_at': datetime.now()
                        })

            loop.run_until_complete(update_task_item())
            
            return {
                'task_item_id': task_item_id,
                'file_id': file_id,
                'plugin_code': plugin_code,
                'plugin_version': plugin_version,
                'result': result,
                'status': 'completed'
            }
            
        finally:
            loop.close()
            
    except Exception as exc:
        logger.error(f"Processing task item {task_item_id} failed: {exc}", exc_info=True)
        
        # 更新任务项状态为失败
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            async def update_failed_task_item():
                async with get_async_db() as db:
                    # 获取任务项对象
                    task_item = await task_item_crud.get(db, id=task_item_id)
                    if task_item:
                        await task_item_crud.update(db, db_obj=task_item, obj_in={
                            'status': 'failed',
                            'error': str(exc),
                            'completed_at': datetime.now()
                        })

            loop.run_until_complete(update_failed_task_item())
        finally:
            loop.close()
        
        raise


@celery_app.task(
    bind=True,
    name='process_task_workflow',
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 2},
    retry_backoff=True
)
def process_task_workflow(self, task_id: int, download_results: List[Dict], plugins: List[Dict]) -> Dict[str, Any]:
    """
    处理任务工作流 - 在文件下载完成后执行
    
    Args:
        task_id: 任务ID
        download_results: 下载结果列表
        plugins: 插件配置列表
        
    Returns:
        dict: 工作流处理结果
    """
    try:
        logger.info(f"Starting workflow for task {task_id}")
        
        # 筛选成功下载的文件
        successful_downloads = [r for r in download_results if r.get('status') == 'success']
        
        if not successful_downloads:
            logger.warning(f"Task {task_id}: No successful downloads, skipping processing")
            return {
                'task_id': task_id,
                'status': 'completed',
                'message': 'No files to process'
            }
        
        # 创建处理任务项
        processing_jobs = []
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            async def create_task_items():
                task_items = []
                async with get_async_db() as db:
                    for download_result in successful_downloads:
                        file_id = download_result['file_id']
                        file_hash = download_result.get('file_hash', '')

                        for plugin_config in plugins:
                            # 创建任务项记录
                            task_item_data = {
                                'task_id': task_id,
                                'file_id': file_id,
                                'file_hash': file_hash,
                                'plugin_code': plugin_config['plugin_code'],
                                'plugin_version': plugin_config['plugin_version'],
                                'params': plugin_config.get('params', {}),
                                'status': 'pending',
                                'file_url': download_result.get('url', ''),
                                'media_type': 'visible_light',  # 默认媒体类型，实际应该从文件信息获取
                                'file_type': 'image'  # 默认文件类型，实际应该从文件信息获取
                            }

                            task_item = await task_item_crud.create(db, obj_in=task_item_data)
                            task_items.append(task_item)

                return task_items
            
            task_items = loop.run_until_complete(create_task_items())
            
        finally:
            loop.close()
        
        # 创建并行处理任务
        for task_item in task_items:
            job = process_task_item.s(
                task_item_id=task_item.id,
                file_id=task_item.file_id,
                plugin_code=task_item.plugin_code,
                plugin_version=task_item.plugin_version,
                params=task_item.params
            )
            processing_jobs.append(job)
        
        if processing_jobs:
            # 使用group并行执行所有处理任务
            job_group = group(processing_jobs)
            result = job_group.apply_async()
            
            # 等待所有处理完成
            processing_results = result.get()
            
            # 统计结果
            success_count = sum(1 for r in processing_results if r.get('status') == 'completed')
            failed_count = len(processing_results) - success_count
            
            # 更新任务状态
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                async def update_task_status():
                    async with get_async_db() as db:
                        # 获取任务对象
                        task = await task_crud.get(db, id=task_id)
                        if task:
                            final_status = 'completed' if failed_count == 0 else ('partial' if success_count > 0 else 'failed')
                            await task_crud.update(db, db_obj=task, obj_in={
                                'status': final_status,
                                'completed_at': datetime.now()
                            })
                
                loop.run_until_complete(update_task_status())
                
            finally:
                loop.close()
            
            return {
                'task_id': task_id,
                'total_items': len(processing_results),
                'success_count': success_count,
                'failed_count': failed_count,
                'status': 'completed'
            }
        
        return {
            'task_id': task_id,
            'status': 'completed',
            'message': 'No processing jobs created'
        }
        
    except Exception as exc:
        logger.error(f"Workflow for task {task_id} failed: {exc}", exc_info=True)
        
        # 更新任务状态为失败
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            async def update_failed_task():
                async with get_async_db() as db:
                    # 获取任务对象
                    task = await task_crud.get(db, id=task_id)
                    if task:
                        await task_crud.update(db, db_obj=task, obj_in={
                            'status': 'failed',
                            'error': str(exc),
                            'completed_at': datetime.now()
                        })
            
            loop.run_until_complete(update_failed_task())
        finally:
            loop.close()
        
        raise


@celery_app.task(name='test_processing_task')
def test_processing_task(data: dict) -> Dict[str, Any]:
    """测试处理任务"""
    return {
        'input': data,
        'status': 'test_success',
        'message': 'This is a test processing task'
    }
