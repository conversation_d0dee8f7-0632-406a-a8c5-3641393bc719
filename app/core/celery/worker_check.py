"""
Celery Worker状态检查工具
"""
from typing import Dict, List, Any
from datetime import datetime
from app.core.celery_app import celery_app
from app.log import logger


def check_celery_workers() -> Dict[str, Any]:
    """
    检查Celery Worker状态
    
    Returns:
        Dict[str, Any]: Worker状态信息
    """
    try:
        # 获取活跃的worker
        inspect = celery_app.control.inspect()
        
        # 检查活跃worker
        active_workers = inspect.active()
        if not active_workers:
            return {
                "status": "no_workers",
                "message": "No active Celery workers found",
                "workers": [],
                "queues": []
            }
        
        # 获取worker统计信息
        stats = inspect.stats()
        registered_tasks = inspect.registered()
        active_queues = inspect.active_queues()
        
        worker_info = []
        for worker_name in active_workers.keys():
            worker_data = {
                "name": worker_name,
                "active_tasks": len(active_workers.get(worker_name, [])),
                "registered_tasks": len(registered_tasks.get(worker_name, [])),
                "queues": [q['name'] for q in active_queues.get(worker_name, [])]
            }
            
            if stats and worker_name in stats:
                worker_stats = stats[worker_name]
                worker_data.update({
                    "total_tasks": worker_stats.get('total', {}),
                    "pool": worker_stats.get('pool', {}),
                    "rusage": worker_stats.get('rusage', {})
                })
            
            worker_info.append(worker_data)
        
        return {
            "status": "healthy",
            "message": f"Found {len(worker_info)} active workers",
            "workers": worker_info,
            "total_workers": len(worker_info),
            "total_active_tasks": sum(w["active_tasks"] for w in worker_info)
        }
        
    except Exception as e:
        logger.error(f"Failed to check Celery workers: {e}", exc_info=True)
        return {
            "status": "error",
            "message": f"Failed to check workers: {str(e)}",
            "workers": [],
            "error": str(e)
        }


def check_celery_queues() -> Dict[str, Any]:
    """
    检查Celery队列状态
    
    Returns:
        Dict[str, Any]: 队列状态信息
    """
    try:
        inspect = celery_app.control.inspect()
        
        # 获取队列信息
        active_queues = inspect.active_queues()
        if not active_queues:
            return {
                "status": "no_queues",
                "message": "No active queues found",
                "queues": []
            }
        
        queue_info = {}
        for worker_name, queues in active_queues.items():
            for queue in queues:
                queue_name = queue['name']
                if queue_name not in queue_info:
                    queue_info[queue_name] = {
                        "name": queue_name,
                        "workers": [],
                        "routing_key": queue.get('routing_key', ''),
                        "exchange": queue.get('exchange', {})
                    }
                queue_info[queue_name]["workers"].append(worker_name)
        
        return {
            "status": "healthy",
            "message": f"Found {len(queue_info)} active queues",
            "queues": list(queue_info.values()),
            "total_queues": len(queue_info)
        }
        
    except Exception as e:
        logger.error(f"Failed to check Celery queues: {e}", exc_info=True)
        return {
            "status": "error",
            "message": f"Failed to check queues: {str(e)}",
            "queues": [],
            "error": str(e)
        }


def submit_test_task() -> Dict[str, Any]:
    """
    提交测试任务验证Celery是否正常工作
    
    Returns:
        Dict[str, Any]: 测试结果
    """
    try:
        from app.core.celery.download_tasks import test_download_task
        
        # 提交测试任务
        result = test_download_task.delay("test_data")
        
        return {
            "status": "submitted",
            "message": "Test task submitted successfully",
            "task_id": result.id,
            "task_status": result.status
        }
        
    except Exception as e:
        logger.error(f"Failed to submit test task: {e}", exc_info=True)
        return {
            "status": "error",
            "message": f"Failed to submit test task: {str(e)}",
            "error": str(e)
        }


def get_comprehensive_status() -> Dict[str, Any]:
    """
    获取Celery的综合状态信息
    
    Returns:
        Dict[str, Any]: 综合状态信息
    """
    worker_status = check_celery_workers()
    queue_status = check_celery_queues()
    
    # 判断整体健康状态
    overall_healthy = (
        worker_status["status"] == "healthy" and 
        queue_status["status"] == "healthy"
    )
    
    return {
        "overall_status": "healthy" if overall_healthy else "unhealthy",
        "timestamp": str(datetime.now()),
        "workers": worker_status,
        "queues": queue_status,
        "recommendations": get_recommendations(worker_status, queue_status)
    }


def get_recommendations(worker_status: Dict, queue_status: Dict) -> List[str]:
    """
    根据状态提供建议
    
    Args:
        worker_status: Worker状态
        queue_status: 队列状态
        
    Returns:
        List[str]: 建议列表
    """
    recommendations = []
    
    if worker_status["status"] == "no_workers":
        recommendations.append("启动Celery Worker: celery -A app.core.celery_app worker --loglevel=info")
    
    if queue_status["status"] == "no_queues":
        recommendations.append("检查Celery配置中的队列设置")
    
    if worker_status["status"] == "healthy":
        total_workers = worker_status.get("total_workers", 0)
        if total_workers < 2:
            recommendations.append("考虑启动更多Worker以提高处理能力")
    
    if not recommendations:
        recommendations.append("Celery系统运行正常")
    
    return recommendations


if __name__ == "__main__":
    # 命令行检查工具
    import sys
    from datetime import datetime
    
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        # 测试模式
        print("Testing Celery system...")
        status = get_comprehensive_status()
        print(f"Overall Status: {status['overall_status']}")
        print(f"Workers: {status['workers']['total_workers']}")
        print(f"Queues: {status['queues']['total_queues']}")
        
        for rec in status['recommendations']:
            print(f"💡 {rec}")
    else:
        # 简单检查
        worker_status = check_celery_workers()
        print(f"Celery Workers: {worker_status['status']}")
        print(f"Message: {worker_status['message']}")
