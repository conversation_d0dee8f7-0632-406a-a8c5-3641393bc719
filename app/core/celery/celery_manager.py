"""
基于Celery的任务管理器
"""
import asyncio
import copy
from typing import List, Dict, Any
from datetime import datetime
from celery import group, chord
from sqlalchemy.ext.asyncio import AsyncSession

from app.schemas.task import TaskCreate, TaskResponse
from app.core.crud.task import task_crud
from app.core.celery.download_tasks import download_file_task, batch_download_files
from app.core.celery.processing_tasks import process_task_workflow
from app.core.celery.management_tasks import update_task_status
from app.core.celery_app import celery_app
from app.log import logger


class CeleryTaskManager:
    """基于Celery的任务管理器"""
    
    async def create_task(self, db: AsyncSession, task_in: TaskCreate) -> TaskResponse:
        """
        创建任务并提交到Celery
        
        Args:
            db: 数据库会话
            task_in: 任务创建请求
            
        Returns:
            TaskResponse: 任务响应
        """
        try:
            # 计算总任务项数量
            total_items_count = len(task_in.items) * len(task_in.plugins)
            
            # 1. 创建数据库任务记录
            task_data = {
                "total_items": total_items_count,
                "status": "pending",
                "priority": task_in.priority
            }
            
            task = await task_crud.create(db, obj_in=task_data)
            logger.info(f"Created task {task.id} with {total_items_count} items")
            
            # 2. 准备下载任务数据
            urls_and_types = [(item.file_url, item.file_type) for item in task_in.items]
            plugins_data = [plugin.dict() for plugin in task_in.plugins]
            
            # 3. 创建Celery工作流
            if len(urls_and_types) == 1:
                # 单文件下载
                download_task = download_file_task.s(
                    url=urls_and_types[0][0],
                    file_type=urls_and_types[0][1],
                    task_id=task.id
                )
                
                # 使用chord模式：下载完成后处理
                workflow = chord([download_task])(
                    process_task_workflow.s(task_id=task.id, plugins=plugins_data)
                )
                
            else:
                # 批量文件下载
                download_task = batch_download_files.s(
                    urls_and_types=urls_and_types,
                    task_id=task.id
                )
                
                # 下载完成后处理
                workflow = chord([download_task])(
                    process_task_workflow.s(task_id=task.id, plugins=plugins_data)
                )
            
            # 4. 更新任务记录，保存Celery任务ID
            updated_task = await task_crud.update(db, db_obj=task, obj_in={
                "celery_task_id": workflow.id,
                "status": "running",
                "started_at": datetime.now()
            })

            logger.info(f"Task {task.id} submitted to Celery with workflow ID: {workflow.id}")

            # 5. 返回任务信息
            return await self.get_task_progress(db, task=updated_task)
            
        except Exception as e:
            logger.error(f"Failed to create Celery task: {str(e)}", exc_info=True)
            
            # 更新任务状态为失败
            if 'task' in locals():
                await task_crud.update(db, db_obj=task, obj_in={
                    "status": "failed",
                    "error": f"Task creation failed: {str(e)}",
                    "completed_at": datetime.now()
                })
            
            raise
    
    async def cancel_task(self, db: AsyncSession, task_id: int) -> bool:
        """
        取消任务
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            
        Returns:
            bool: 是否成功取消
        """
        try:
            # 获取任务信息
            task = await task_crud.get(db, id=task_id)
            if not task:
                logger.warning(f"Task {task_id} not found")
                return False
            
            # 如果任务已完成，无法取消
            if task.status in ['completed', 'failed', 'canceled']:
                logger.warning(f"Task {task_id} is already {task.status}, cannot cancel")
                return False
            
            # 取消Celery任务
            if task.celery_task_id:
                celery_app.control.revoke(task.celery_task_id, terminate=True)
                logger.info(f"Revoked Celery task {task.celery_task_id}")
            
            # 更新数据库状态
            await task_crud.update(db, db_obj=task, obj_in={
                "status": "canceled",
                "completed_at": datetime.now()
            })
            
            logger.info(f"Task {task_id} canceled successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to cancel task {task_id}: {str(e)}", exc_info=True)
            return False
    
    async def get_task_status(self, db: AsyncSession, task_id: int) -> Dict[str, Any]:
        """
        获取任务状态（包括Celery状态）
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            
        Returns:
            dict: 任务状态信息
        """
        try:
            # 获取数据库中的任务信息
            task = await task_crud.get(db, id=task_id)
            if not task:
                return {"error": "Task not found"}
            
            result = {
                "task_id": task_id,
                "db_status": task.status,
                "created_at": task.created_at.isoformat() if task.created_at else None,
                "updated_at": task.updated_at.isoformat() if task.updated_at else None,
                "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                "error": task.error
            }
            
            # 如果有Celery任务ID，获取Celery状态
            if task.celery_task_id:
                try:
                    celery_result = celery_app.AsyncResult(task.celery_task_id)
                    result.update({
                        "celery_task_id": task.celery_task_id,
                        "celery_status": celery_result.status,
                        "celery_result": celery_result.result,
                        "celery_traceback": celery_result.traceback
                    })
                except Exception as e:
                    result["celery_error"] = str(e)
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to get task status for {task_id}: {str(e)}", exc_info=True)
            return {"error": str(e)}
    
    async def get_task_progress(self, db: AsyncSession, task: Any = None, task_id: int = None) -> TaskResponse:
        """
        获取任务进度信息
        
        Args:
            db: 数据库会话
            task: 任务对象（可选）
            task_id: 任务ID（可选）
            
        Returns:
            TaskResponse: 任务响应
        """
        try:
            if task is None:
                if task_id is None:
                    raise ValueError("Either task or task_id must be provided")
                task = await task_crud.get(db, id=task_id)
                if not task:
                    raise ValueError(f"Task {task_id} not found")
            
            # 获取任务项进度
            from app.core.crud.task_item import task_item_crud
            completed_count = await task_item_crud.count_by_status(
                db, task_id=task.id, statuses=['completed', 'failed', 'canceled']
            )

            # 检查Celery任务状态
            celery_status = None
            if task.celery_task_id:
                try:
                    celery_result = celery_app.AsyncResult(task.celery_task_id)
                    celery_status = celery_result.status
                    logger.debug(f"Task {task.id} Celery status: {celery_status}")
                except Exception as e:
                    logger.warning(f"Failed to get Celery status for task {task.id}: {e}")

            # 构建响应
            response = TaskResponse(
                taskId=str(task.id),
                progress=f"{completed_count}/{task.total_items}",
                status=task.status,
                priority=task.priority,
                createdAt=task.created_at.strftime("%Y-%m-%d %H:%M:%S") if task.created_at else None,
                updatedAt=task.updated_at.strftime("%Y-%m-%d %H:%M:%S") if task.updated_at else None,
                startedAt=task.started_at.strftime("%Y-%m-%d %H:%M:%S") if task.started_at else None,
                completedAt=task.completed_at.strftime("%Y-%m-%d %H:%M:%S") if task.completed_at else None,
                error=task.error
            )

            # 添加Celery状态信息（用于调试）
            if celery_status:
                logger.info(f"Task {task.id} - DB status: {task.status}, Celery status: {celery_status}")

            return response
            
        except Exception as e:
            logger.error(f"Failed to get task progress: {str(e)}", exc_info=True)
            raise
    
    async def retry_failed_task(self, db: AsyncSession, task_id: int) -> bool:
        """
        重试失败的任务
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            
        Returns:
            bool: 是否成功重试
        """
        try:
            task = await task_crud.get(db, id=task_id)
            if not task:
                logger.warning(f"Task {task_id} not found")
                return False
            
            if task.status != 'failed':
                logger.warning(f"Task {task_id} is not failed, cannot retry")
                return False
            
            # 重置任务状态
            await task_crud.update(db, db_obj=task, obj_in={
                "status": "pending",
                "error": None,
                "completed_at": None,
                "celery_task_id": None
            })
            
            # 重新创建任务（需要原始的TaskCreate数据，这里简化处理）
            # 实际实现中可能需要存储原始请求数据
            logger.info(f"Task {task_id} reset for retry")
            return True
            
        except Exception as e:
            logger.error(f"Failed to retry task {task_id}: {str(e)}", exc_info=True)
            return False


# 创建全局Celery任务管理器实例
celery_task_manager = CeleryTaskManager()
