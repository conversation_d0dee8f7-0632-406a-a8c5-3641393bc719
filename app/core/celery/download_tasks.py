"""
文件下载任务
"""
import asyncio
from typing import Dict, Any, Optional
from celery import Task
from celery.exceptions import Retry
import httpx

from app.core.celery_app import celery_app
from app.core.file.manager import file_manager
from app.log import logger


class DownloadTask(Task):
    """文件下载任务基类"""
    
    def on_success(self, retval, task_id, args, kwargs):
        """任务成功回调"""
        logger.info(f"Download task {task_id} succeeded: {retval.get('file_id', 'unknown')}")
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """任务失败回调"""
        logger.error(f"Download task {task_id} failed: {exc}")
        # 这里可以添加失败通知逻辑
    
    def on_retry(self, exc, task_id, args, kwargs, einfo):
        """任务重试回调"""
        logger.warning(f"Download task {task_id} retrying (attempt {self.request.retries + 1}): {exc}")


@celery_app.task(
    bind=True,
    base=DownloadTask,
    name='download_file_task',
    autoretry_for=(
        httpx.HTTPStatusError,
        httpx.ConnectError,
        httpx.TimeoutException,
        httpx.RequestError,
        OSError,
        ConnectionError
    ),
    retry_kwargs={'max_retries': 5},
    retry_backoff=True,
    retry_backoff_max=300,
    retry_jitter=True
)
def download_file_task(self, url: str, file_type: str = None, task_id: int = None) -> Dict[str, Any]:
    """
    文件下载任务 - 支持自动重试
    
    Args:
        url: 文件URL
        file_type: 文件类型
        task_id: 关联的任务ID (用于状态更新)
        
    Returns:
        dict: 下载结果
        {
            'file_id': int,
            'file_hash': str,
            'local_path': str,
            'status': 'success'|'failed',
            'error': str (if failed)
        }
    """
    try:
        logger.info(f"Starting download: {url}")

        # 创建新的事件循环来运行异步代码
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            # 在事件循环中运行下载任务
            async def download_task():
                try:
                    # 使用file_manager进行下载，但捕获并重新抛出原始异常
                    file = await file_manager.download_from_url(
                        url=url,
                        file_type=file_type,
                        save_to_storage=True
                    )

                    return {
                        'file_id': file.id,
                        'file_hash': file.file_hash,
                        'local_path': file.local_path,
                        'url': file.url,
                        'status': 'success'
                    }

                except HTTPException as http_exc:
                    # 将HTTPException转换回原始异常类型以便Celery重试
                    if "HTTP 403" in str(http_exc.detail):
                        # 创建一个403 HTTPStatusError
                        import httpx
                        response = httpx.Response(403)
                        raise httpx.HTTPStatusError("403 Forbidden", request=None, response=response)
                    elif "HTTP 404" in str(http_exc.detail):
                        # 404错误不应该重试
                        raise ValueError(f"File not found: {url}")
                    else:
                        # 其他HTTP错误
                        raise ConnectionError(str(http_exc.detail))

                except Exception as e:
                    logger.error(f"Download failed in async context: {str(e)}", exc_info=True)
                    raise

            result = loop.run_until_complete(download_task())
            logger.info(f"Download completed: {url} -> file_id: {result['file_id']}")
            return result

        finally:
            loop.close()
            
    except httpx.HTTPStatusError as exc:
        error_msg = f"HTTP {exc.response.status_code} error for {url}"
        logger.error(error_msg)

        # 对于某些HTTP错误，不进行重试
        if exc.response.status_code in [404, 410]:  # Not Found, Gone
            return {
                'status': 'failed',
                'error': error_msg,
                'url': url,
                'no_retry': True
            }

        # 对于403错误，记录详细信息（STS重试应该在downloader层处理）
        if exc.response.status_code == 403:
            logger.warning(f"403 Forbidden error for {url} - STS retry should be handled by downloader")

        # 其他HTTP错误会被自动重试
        raise
        
    except (httpx.ConnectError, httpx.TimeoutException, ConnectionError) as exc:
        error_msg = f"Network error for {url}: {str(exc)}"
        logger.error(error_msg)
        # 网络错误会被自动重试
        raise
        
    except Exception as exc:
        error_msg = f"Unexpected error downloading {url}: {str(exc)}"
        logger.error(error_msg, exc_info=True)
        
        # 对于未知错误，也进行重试
        raise


@celery_app.task(
    bind=True,
    name='batch_download_files',
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 3},
    retry_backoff=True
)
def batch_download_files(self, urls_and_types: list, task_id: int = None) -> Dict[str, Any]:
    """
    批量下载文件任务
    
    Args:
        urls_and_types: [(url, file_type), ...] 列表
        task_id: 关联的任务ID
        
    Returns:
        dict: 批量下载结果
    """
    try:
        from celery import group
        
        # 创建并行下载任务组
        download_jobs = []
        for url, file_type in urls_and_types:
            job = download_file_task.s(url=url, file_type=file_type, task_id=task_id)
            download_jobs.append(job)
        
        # 执行并行下载
        job_group = group(download_jobs)
        result = job_group.apply_async()
        
        # 等待所有下载完成
        results = result.get()
        
        # 统计结果
        success_count = sum(1 for r in results if r.get('status') == 'success')
        failed_count = len(results) - success_count
        
        return {
            'total': len(results),
            'success': success_count,
            'failed': failed_count,
            'results': results,
            'status': 'completed'
        }
        
    except Exception as exc:
        logger.error(f"Batch download failed: {exc}", exc_info=True)
        raise


# 用于测试的简单任务
@celery_app.task(name='test_download_task')
def test_download_task(url: str) -> Dict[str, Any]:
    """测试下载任务"""
    return {
        'url': url,
        'status': 'test_success',
        'message': 'This is a test task'
    }
